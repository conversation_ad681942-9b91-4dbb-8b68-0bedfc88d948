(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const f of o)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&s(d)}).observe(document,{childList:!0,subtree:!0});function r(o){const f={};return o.integrity&&(f.integrity=o.integrity),o.referrerPolicy&&(f.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?f.credentials="include":o.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function s(o){if(o.ep)return;o.ep=!0;const f=r(o);fetch(o.href,f)}})();function qf(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var ef={exports:{}},Or={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $p;function Ob(){if($p)return Or;$p=1;var l=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function r(s,o,f){var d=null;if(f!==void 0&&(d=""+f),o.key!==void 0&&(d=""+o.key),"key"in o){f={};for(var h in o)h!=="key"&&(f[h]=o[h])}else f=o;return o=f.ref,{$$typeof:l,type:s,key:d,ref:o!==void 0?o:null,props:f}}return Or.Fragment=a,Or.jsx=r,Or.jsxs=r,Or}var Xp;function jb(){return Xp||(Xp=1,ef.exports=Ob()),ef.exports}var v=jb(),tf={exports:{}},Oe={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zp;function Db(){if(Zp)return Oe;Zp=1;var l=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),S=Symbol.iterator;function x(O){return O===null||typeof O!="object"?null:(O=S&&O[S]||O["@@iterator"],typeof O=="function"?O:null)}var R={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},E=Object.assign,C={};function D(O,Y,ae){this.props=O,this.context=Y,this.refs=C,this.updater=ae||R}D.prototype.isReactComponent={},D.prototype.setState=function(O,Y){if(typeof O!="object"&&typeof O!="function"&&O!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,O,Y,"setState")},D.prototype.forceUpdate=function(O){this.updater.enqueueForceUpdate(this,O,"forceUpdate")};function w(){}w.prototype=D.prototype;function T(O,Y,ae){this.props=O,this.context=Y,this.refs=C,this.updater=ae||R}var M=T.prototype=new w;M.constructor=T,E(M,D.prototype),M.isPureReactComponent=!0;var X=Array.isArray,V={H:null,A:null,T:null,S:null,V:null},I=Object.prototype.hasOwnProperty;function W(O,Y,ae,ee,he,je){return ae=je.ref,{$$typeof:l,type:O,key:Y,ref:ae!==void 0?ae:null,props:je}}function se(O,Y){return W(O.type,Y,void 0,void 0,void 0,O.props)}function fe(O){return typeof O=="object"&&O!==null&&O.$$typeof===l}function De(O){var Y={"=":"=0",":":"=2"};return"$"+O.replace(/[=:]/g,function(ae){return Y[ae]})}var ge=/\/+/g;function re(O,Y){return typeof O=="object"&&O!==null&&O.key!=null?De(""+O.key):Y.toString(36)}function ye(){}function de(O){switch(O.status){case"fulfilled":return O.value;case"rejected":throw O.reason;default:switch(typeof O.status=="string"?O.then(ye,ye):(O.status="pending",O.then(function(Y){O.status==="pending"&&(O.status="fulfilled",O.value=Y)},function(Y){O.status==="pending"&&(O.status="rejected",O.reason=Y)})),O.status){case"fulfilled":return O.value;case"rejected":throw O.reason}}throw O}function _e(O,Y,ae,ee,he){var je=typeof O;(je==="undefined"||je==="boolean")&&(O=null);var xe=!1;if(O===null)xe=!0;else switch(je){case"bigint":case"string":case"number":xe=!0;break;case"object":switch(O.$$typeof){case l:case a:xe=!0;break;case g:return xe=O._init,_e(xe(O._payload),Y,ae,ee,he)}}if(xe)return he=he(O),xe=ee===""?"."+re(O,0):ee,X(he)?(ae="",xe!=null&&(ae=xe.replace(ge,"$&/")+"/"),_e(he,Y,ae,"",function(bt){return bt})):he!=null&&(fe(he)&&(he=se(he,ae+(he.key==null||O&&O.key===he.key?"":(""+he.key).replace(ge,"$&/")+"/")+xe)),Y.push(he)),1;xe=0;var at=ee===""?".":ee+":";if(X(O))for(var Te=0;Te<O.length;Te++)ee=O[Te],je=at+re(ee,Te),xe+=_e(ee,Y,ae,je,he);else if(Te=x(O),typeof Te=="function")for(O=Te.call(O),Te=0;!(ee=O.next()).done;)ee=ee.value,je=at+re(ee,Te++),xe+=_e(ee,Y,ae,je,he);else if(je==="object"){if(typeof O.then=="function")return _e(de(O),Y,ae,ee,he);throw Y=String(O),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(O).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return xe}function H(O,Y,ae){if(O==null)return O;var ee=[],he=0;return _e(O,ee,"","",function(je){return Y.call(ae,je,he++)}),ee}function J(O){if(O._status===-1){var Y=O._result;Y=Y(),Y.then(function(ae){(O._status===0||O._status===-1)&&(O._status=1,O._result=ae)},function(ae){(O._status===0||O._status===-1)&&(O._status=2,O._result=ae)}),O._status===-1&&(O._status=0,O._result=Y)}if(O._status===1)return O._result.default;throw O._result}var te=typeof reportError=="function"?reportError:function(O){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof O=="object"&&O!==null&&typeof O.message=="string"?String(O.message):String(O),error:O});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",O);return}console.error(O)};function we(){}return Oe.Children={map:H,forEach:function(O,Y,ae){H(O,function(){Y.apply(this,arguments)},ae)},count:function(O){var Y=0;return H(O,function(){Y++}),Y},toArray:function(O){return H(O,function(Y){return Y})||[]},only:function(O){if(!fe(O))throw Error("React.Children.only expected to receive a single React element child.");return O}},Oe.Component=D,Oe.Fragment=r,Oe.Profiler=o,Oe.PureComponent=T,Oe.StrictMode=s,Oe.Suspense=p,Oe.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=V,Oe.__COMPILER_RUNTIME={__proto__:null,c:function(O){return V.H.useMemoCache(O)}},Oe.cache=function(O){return function(){return O.apply(null,arguments)}},Oe.cloneElement=function(O,Y,ae){if(O==null)throw Error("The argument must be a React element, but you passed "+O+".");var ee=E({},O.props),he=O.key,je=void 0;if(Y!=null)for(xe in Y.ref!==void 0&&(je=void 0),Y.key!==void 0&&(he=""+Y.key),Y)!I.call(Y,xe)||xe==="key"||xe==="__self"||xe==="__source"||xe==="ref"&&Y.ref===void 0||(ee[xe]=Y[xe]);var xe=arguments.length-2;if(xe===1)ee.children=ae;else if(1<xe){for(var at=Array(xe),Te=0;Te<xe;Te++)at[Te]=arguments[Te+2];ee.children=at}return W(O.type,he,void 0,void 0,je,ee)},Oe.createContext=function(O){return O={$$typeof:d,_currentValue:O,_currentValue2:O,_threadCount:0,Provider:null,Consumer:null},O.Provider=O,O.Consumer={$$typeof:f,_context:O},O},Oe.createElement=function(O,Y,ae){var ee,he={},je=null;if(Y!=null)for(ee in Y.key!==void 0&&(je=""+Y.key),Y)I.call(Y,ee)&&ee!=="key"&&ee!=="__self"&&ee!=="__source"&&(he[ee]=Y[ee]);var xe=arguments.length-2;if(xe===1)he.children=ae;else if(1<xe){for(var at=Array(xe),Te=0;Te<xe;Te++)at[Te]=arguments[Te+2];he.children=at}if(O&&O.defaultProps)for(ee in xe=O.defaultProps,xe)he[ee]===void 0&&(he[ee]=xe[ee]);return W(O,je,void 0,void 0,null,he)},Oe.createRef=function(){return{current:null}},Oe.forwardRef=function(O){return{$$typeof:h,render:O}},Oe.isValidElement=fe,Oe.lazy=function(O){return{$$typeof:g,_payload:{_status:-1,_result:O},_init:J}},Oe.memo=function(O,Y){return{$$typeof:m,type:O,compare:Y===void 0?null:Y}},Oe.startTransition=function(O){var Y=V.T,ae={};V.T=ae;try{var ee=O(),he=V.S;he!==null&&he(ae,ee),typeof ee=="object"&&ee!==null&&typeof ee.then=="function"&&ee.then(we,te)}catch(je){te(je)}finally{V.T=Y}},Oe.unstable_useCacheRefresh=function(){return V.H.useCacheRefresh()},Oe.use=function(O){return V.H.use(O)},Oe.useActionState=function(O,Y,ae){return V.H.useActionState(O,Y,ae)},Oe.useCallback=function(O,Y){return V.H.useCallback(O,Y)},Oe.useContext=function(O){return V.H.useContext(O)},Oe.useDebugValue=function(){},Oe.useDeferredValue=function(O,Y){return V.H.useDeferredValue(O,Y)},Oe.useEffect=function(O,Y,ae){var ee=V.H;if(typeof ae=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return ee.useEffect(O,Y)},Oe.useId=function(){return V.H.useId()},Oe.useImperativeHandle=function(O,Y,ae){return V.H.useImperativeHandle(O,Y,ae)},Oe.useInsertionEffect=function(O,Y){return V.H.useInsertionEffect(O,Y)},Oe.useLayoutEffect=function(O,Y){return V.H.useLayoutEffect(O,Y)},Oe.useMemo=function(O,Y){return V.H.useMemo(O,Y)},Oe.useOptimistic=function(O,Y){return V.H.useOptimistic(O,Y)},Oe.useReducer=function(O,Y,ae){return V.H.useReducer(O,Y,ae)},Oe.useRef=function(O){return V.H.useRef(O)},Oe.useState=function(O){return V.H.useState(O)},Oe.useSyncExternalStore=function(O,Y,ae){return V.H.useSyncExternalStore(O,Y,ae)},Oe.useTransition=function(){return V.H.useTransition()},Oe.version="19.1.0",Oe}var Qp;function Vf(){return Qp||(Qp=1,tf.exports=Db()),tf.exports}var _=Vf();const Je=qf(_);var nf={exports:{}},jr={},lf={exports:{}},af={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kp;function Cb(){return Kp||(Kp=1,function(l){function a(H,J){var te=H.length;H.push(J);e:for(;0<te;){var we=te-1>>>1,O=H[we];if(0<o(O,J))H[we]=J,H[te]=O,te=we;else break e}}function r(H){return H.length===0?null:H[0]}function s(H){if(H.length===0)return null;var J=H[0],te=H.pop();if(te!==J){H[0]=te;e:for(var we=0,O=H.length,Y=O>>>1;we<Y;){var ae=2*(we+1)-1,ee=H[ae],he=ae+1,je=H[he];if(0>o(ee,te))he<O&&0>o(je,ee)?(H[we]=je,H[he]=te,we=he):(H[we]=ee,H[ae]=te,we=ae);else if(he<O&&0>o(je,te))H[we]=je,H[he]=te,we=he;else break e}}return J}function o(H,J){var te=H.sortIndex-J.sortIndex;return te!==0?te:H.id-J.id}if(l.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;l.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();l.unstable_now=function(){return d.now()-h}}var p=[],m=[],g=1,S=null,x=3,R=!1,E=!1,C=!1,D=!1,w=typeof setTimeout=="function"?setTimeout:null,T=typeof clearTimeout=="function"?clearTimeout:null,M=typeof setImmediate<"u"?setImmediate:null;function X(H){for(var J=r(m);J!==null;){if(J.callback===null)s(m);else if(J.startTime<=H)s(m),J.sortIndex=J.expirationTime,a(p,J);else break;J=r(m)}}function V(H){if(C=!1,X(H),!E)if(r(p)!==null)E=!0,I||(I=!0,re());else{var J=r(m);J!==null&&_e(V,J.startTime-H)}}var I=!1,W=-1,se=5,fe=-1;function De(){return D?!0:!(l.unstable_now()-fe<se)}function ge(){if(D=!1,I){var H=l.unstable_now();fe=H;var J=!0;try{e:{E=!1,C&&(C=!1,T(W),W=-1),R=!0;var te=x;try{t:{for(X(H),S=r(p);S!==null&&!(S.expirationTime>H&&De());){var we=S.callback;if(typeof we=="function"){S.callback=null,x=S.priorityLevel;var O=we(S.expirationTime<=H);if(H=l.unstable_now(),typeof O=="function"){S.callback=O,X(H),J=!0;break t}S===r(p)&&s(p),X(H)}else s(p);S=r(p)}if(S!==null)J=!0;else{var Y=r(m);Y!==null&&_e(V,Y.startTime-H),J=!1}}break e}finally{S=null,x=te,R=!1}J=void 0}}finally{J?re():I=!1}}}var re;if(typeof M=="function")re=function(){M(ge)};else if(typeof MessageChannel<"u"){var ye=new MessageChannel,de=ye.port2;ye.port1.onmessage=ge,re=function(){de.postMessage(null)}}else re=function(){w(ge,0)};function _e(H,J){W=w(function(){H(l.unstable_now())},J)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(H){H.callback=null},l.unstable_forceFrameRate=function(H){0>H||125<H?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):se=0<H?Math.floor(1e3/H):5},l.unstable_getCurrentPriorityLevel=function(){return x},l.unstable_next=function(H){switch(x){case 1:case 2:case 3:var J=3;break;default:J=x}var te=x;x=J;try{return H()}finally{x=te}},l.unstable_requestPaint=function(){D=!0},l.unstable_runWithPriority=function(H,J){switch(H){case 1:case 2:case 3:case 4:case 5:break;default:H=3}var te=x;x=H;try{return J()}finally{x=te}},l.unstable_scheduleCallback=function(H,J,te){var we=l.unstable_now();switch(typeof te=="object"&&te!==null?(te=te.delay,te=typeof te=="number"&&0<te?we+te:we):te=we,H){case 1:var O=-1;break;case 2:O=250;break;case 5:O=1073741823;break;case 4:O=1e4;break;default:O=5e3}return O=te+O,H={id:g++,callback:J,priorityLevel:H,startTime:te,expirationTime:O,sortIndex:-1},te>we?(H.sortIndex=te,a(m,H),r(p)===null&&H===r(m)&&(C?(T(W),W=-1):C=!0,_e(V,te-we))):(H.sortIndex=O,a(p,H),E||R||(E=!0,I||(I=!0,re()))),H},l.unstable_shouldYield=De,l.unstable_wrapCallback=function(H){var J=x;return function(){var te=x;x=J;try{return H.apply(this,arguments)}finally{x=te}}}}(af)),af}var Jp;function Nb(){return Jp||(Jp=1,lf.exports=Cb()),lf.exports}var rf={exports:{}},Lt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pp;function Mb(){if(Pp)return Lt;Pp=1;var l=Vf();function a(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)m+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var s={d:{f:r,r:function(){throw Error(a(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},o=Symbol.for("react.portal");function f(p,m,g){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:S==null?null:""+S,children:p,containerInfo:m,implementation:g}}var d=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return Lt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,Lt.createPortal=function(p,m){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(a(299));return f(p,m,null,g)},Lt.flushSync=function(p){var m=d.T,g=s.p;try{if(d.T=null,s.p=2,p)return p()}finally{d.T=m,s.p=g,s.d.f()}},Lt.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,s.d.C(p,m))},Lt.prefetchDNS=function(p){typeof p=="string"&&s.d.D(p)},Lt.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var g=m.as,S=h(g,m.crossOrigin),x=typeof m.integrity=="string"?m.integrity:void 0,R=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;g==="style"?s.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:S,integrity:x,fetchPriority:R}):g==="script"&&s.d.X(p,{crossOrigin:S,integrity:x,fetchPriority:R,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},Lt.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var g=h(m.as,m.crossOrigin);s.d.M(p,{crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&s.d.M(p)},Lt.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var g=m.as,S=h(g,m.crossOrigin);s.d.L(p,g,{crossOrigin:S,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},Lt.preloadModule=function(p,m){if(typeof p=="string")if(m){var g=h(m.as,m.crossOrigin);s.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else s.d.m(p)},Lt.requestFormReset=function(p){s.d.r(p)},Lt.unstable_batchedUpdates=function(p,m){return p(m)},Lt.useFormState=function(p,m,g){return d.H.useFormState(p,m,g)},Lt.useFormStatus=function(){return d.H.useHostTransitionStatus()},Lt.version="19.1.0",Lt}var Wp;function Ub(){if(Wp)return rf.exports;Wp=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(a){console.error(a)}}return l(),rf.exports=Mb(),rf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ip;function zb(){if(Ip)return jr;Ip=1;var l=Nb(),a=Vf(),r=Ub();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(s(188))}function p(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,i=t;;){var u=n.return;if(u===null)break;var c=u.alternate;if(c===null){if(i=u.return,i!==null){n=i;continue}break}if(u.child===c.child){for(c=u.child;c;){if(c===n)return h(u),e;if(c===i)return h(u),t;c=c.sibling}throw Error(s(188))}if(n.return!==i.return)n=u,i=c;else{for(var y=!1,b=u.child;b;){if(b===n){y=!0,n=u,i=c;break}if(b===i){y=!0,i=u,n=c;break}b=b.sibling}if(!y){for(b=c.child;b;){if(b===n){y=!0,n=c,i=u;break}if(b===i){y=!0,i=c,n=u;break}b=b.sibling}if(!y)throw Error(s(189))}}if(n.alternate!==i)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var g=Object.assign,S=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),R=Symbol.for("react.portal"),E=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),D=Symbol.for("react.profiler"),w=Symbol.for("react.provider"),T=Symbol.for("react.consumer"),M=Symbol.for("react.context"),X=Symbol.for("react.forward_ref"),V=Symbol.for("react.suspense"),I=Symbol.for("react.suspense_list"),W=Symbol.for("react.memo"),se=Symbol.for("react.lazy"),fe=Symbol.for("react.activity"),De=Symbol.for("react.memo_cache_sentinel"),ge=Symbol.iterator;function re(e){return e===null||typeof e!="object"?null:(e=ge&&e[ge]||e["@@iterator"],typeof e=="function"?e:null)}var ye=Symbol.for("react.client.reference");function de(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ye?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case E:return"Fragment";case D:return"Profiler";case C:return"StrictMode";case V:return"Suspense";case I:return"SuspenseList";case fe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case R:return"Portal";case M:return(e.displayName||"Context")+".Provider";case T:return(e._context.displayName||"Context")+".Consumer";case X:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case W:return t=e.displayName||null,t!==null?t:de(e.type)||"Memo";case se:t=e._payload,e=e._init;try{return de(e(t))}catch{}}return null}var _e=Array.isArray,H=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,te={pending:!1,data:null,method:null,action:null},we=[],O=-1;function Y(e){return{current:e}}function ae(e){0>O||(e.current=we[O],we[O]=null,O--)}function ee(e,t){O++,we[O]=e.current,e.current=t}var he=Y(null),je=Y(null),xe=Y(null),at=Y(null);function Te(e,t){switch(ee(xe,t),ee(je,e),ee(he,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?bp(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=bp(t),e=Sp(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}ae(he),ee(he,e)}function bt(){ae(he),ae(je),ae(xe)}function wt(e){e.memoizedState!==null&&ee(at,e);var t=he.current,n=Sp(t,e.type);t!==n&&(ee(je,e),ee(he,n))}function jt(e){je.current===e&&(ae(he),ae(je)),at.current===e&&(ae(at),wr._currentValue=te)}var mn=Object.prototype.hasOwnProperty,Gt=l.unstable_scheduleCallback,Yn=l.unstable_cancelCallback,Dt=l.unstable_shouldYield,ol=l.unstable_requestPaint,st=l.unstable_now,Rn=l.unstable_getCurrentPriorityLevel,Ye=l.unstable_ImmediatePriority,cl=l.unstable_UserBlockingPriority,nn=l.unstable_NormalPriority,j=l.unstable_LowPriority,B=l.unstable_IdlePriority,G=l.log,ie=l.unstable_setDisableYieldValue,P=null,K=null;function ne(e){if(typeof G=="function"&&ie(e),K&&typeof K.setStrictMode=="function")try{K.setStrictMode(P,e)}catch{}}var ve=Math.clz32?Math.clz32:On,Pe=Math.log,et=Math.LN2;function On(e){return e>>>=0,e===0?32:31-(Pe(e)/et|0)|0}var mt=256,qe=4194304;function $t(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ln(e,t,n){var i=e.pendingLanes;if(i===0)return 0;var u=0,c=e.suspendedLanes,y=e.pingedLanes;e=e.warmLanes;var b=i&134217727;return b!==0?(i=b&~c,i!==0?u=$t(i):(y&=b,y!==0?u=$t(y):n||(n=b&~e,n!==0&&(u=$t(n))))):(b=i&~c,b!==0?u=$t(b):y!==0?u=$t(y):n||(n=i&~e,n!==0&&(u=$t(n)))),u===0?0:t!==0&&t!==u&&(t&c)===0&&(c=u&-u,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:u}function Xt(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Gn(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function fl(){var e=mt;return mt<<=1,(mt&4194048)===0&&(mt=256),e}function dl(){var e=qe;return qe<<=1,(qe&62914560)===0&&(qe=4194304),e}function $n(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function jn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function La(e,t,n,i,u,c){var y=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var b=e.entanglements,A=e.expirationTimes,L=e.hiddenUpdates;for(n=y&~n;0<n;){var $=31-ve(n),Q=1<<$;b[$]=0,A[$]=-1;var k=L[$];if(k!==null)for(L[$]=null,$=0;$<k.length;$++){var q=k[$];q!==null&&(q.lane&=-536870913)}n&=~Q}i!==0&&ut(e,i,0),c!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=c&~(y&~t))}function ut(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-ve(t);e.entangledLanes|=t,e.entanglements[i]=e.entanglements[i]|1073741824|n&4194090}function Ht(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-ve(n),u=1<<i;u&t|e[i]&t&&(e[i]|=t),n&=~u}}function be(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ne(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Ut(){var e=J.p;return e!==0?e:(e=window.event,e===void 0?32:Hp(e.type))}function hl(e,t){var n=J.p;try{return J.p=e,t()}finally{J.p=n}}var le=Math.random().toString(36).slice(2),ue="__reactFiber$"+le,me="__reactProps$"+le,We="__reactContainer$"+le,pt="__reactEvents$"+le,pn="__reactListeners$"+le,yn="__reactHandles$"+le,At="__reactResources$"+le,_t="__reactMarker$"+le;function Ge(e){delete e[ue],delete e[me],delete e[pt],delete e[pn],delete e[yn]}function an(e){var t=e[ue];if(t)return t;for(var n=e.parentNode;n;){if(t=n[We]||n[ue]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ap(e);e!==null;){if(n=e[ue])return n;e=Ap(e)}return t}e=n,n=e.parentNode}return null}function zl(e){if(e=e[ue]||e[We]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ca(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function ml(e){var t=e[At];return t||(t=e[At]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function ot(e){e[_t]=!0}var Ui=new Set,zi={};function Xn(e,t){Le(e,t),Le(e+"Capture",t)}function Le(e,t){for(zi[e]=t,e=0;e<t.length;e++)Ui.add(t[e])}var Dn=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ba={},Ll={};function Xu(e){return mn.call(Ll,e)?!0:mn.call(Ba,e)?!1:Dn.test(e)?Ll[e]=!0:(Ba[e]=!0,!1)}function Bl(e,t,n){if(Xu(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var i=t.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Wr(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function pl(e,t,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+i)}}var Zu,dd;function ka(e){if(Zu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Zu=t&&t[1]||"",dd=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Zu+e+dd}var Qu=!1;function Ku(e,t){if(!e||Qu)return"";Qu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(q){var k=q}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(q){k=q}e.call(Q.prototype)}}else{try{throw Error()}catch(q){k=q}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(q){if(q&&k&&typeof q.stack=="string")return[q.stack,k.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=i.DetermineComponentFrameRoot(),y=c[0],b=c[1];if(y&&b){var A=y.split(`
`),L=b.split(`
`);for(u=i=0;i<A.length&&!A[i].includes("DetermineComponentFrameRoot");)i++;for(;u<L.length&&!L[u].includes("DetermineComponentFrameRoot");)u++;if(i===A.length||u===L.length)for(i=A.length-1,u=L.length-1;1<=i&&0<=u&&A[i]!==L[u];)u--;for(;1<=i&&0<=u;i--,u--)if(A[i]!==L[u]){if(i!==1||u!==1)do if(i--,u--,0>u||A[i]!==L[u]){var $=`
`+A[i].replace(" at new "," at ");return e.displayName&&$.includes("<anonymous>")&&($=$.replace("<anonymous>",e.displayName)),$}while(1<=i&&0<=u);break}}}finally{Qu=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?ka(n):""}function Eg(e){switch(e.tag){case 26:case 27:case 5:return ka(e.type);case 16:return ka("Lazy");case 13:return ka("Suspense");case 19:return ka("SuspenseList");case 0:case 15:return Ku(e.type,!1);case 11:return Ku(e.type.render,!1);case 1:return Ku(e.type,!0);case 31:return ka("Activity");default:return""}}function hd(e){try{var t="";do t+=Eg(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function gn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function md(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function wg(e){var t=md(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var u=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(y){i=""+y,c.call(this,y)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(y){i=""+y},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ir(e){e._valueTracker||(e._valueTracker=wg(e))}function pd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=md(e)?e.checked?"true":"false":e.value),e=i,e!==n?(t.setValue(e),!0):!1}function es(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Ag=/[\n"\\]/g;function vn(e){return e.replace(Ag,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Ju(e,t,n,i,u,c,y,b){e.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.type=y:e.removeAttribute("type"),t!=null?y==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+gn(t)):e.value!==""+gn(t)&&(e.value=""+gn(t)):y!=="submit"&&y!=="reset"||e.removeAttribute("value"),t!=null?Pu(e,y,gn(t)):n!=null?Pu(e,y,gn(n)):i!=null&&e.removeAttribute("value"),u==null&&c!=null&&(e.defaultChecked=!!c),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?e.name=""+gn(b):e.removeAttribute("name")}function yd(e,t,n,i,u,c,y,b){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+gn(n):"",t=t!=null?""+gn(t):n,b||t===e.value||(e.value=t),e.defaultValue=t}i=i??u,i=typeof i!="function"&&typeof i!="symbol"&&!!i,e.checked=b?e.checked:!!i,e.defaultChecked=!!i,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(e.name=y)}function Pu(e,t,n){t==="number"&&es(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Ha(e,t,n,i){if(e=e.options,t){t={};for(var u=0;u<n.length;u++)t["$"+n[u]]=!0;for(n=0;n<e.length;n++)u=t.hasOwnProperty("$"+e[n].value),e[n].selected!==u&&(e[n].selected=u),u&&i&&(e[n].defaultSelected=!0)}else{for(n=""+gn(n),t=null,u=0;u<e.length;u++){if(e[u].value===n){e[u].selected=!0,i&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function gd(e,t,n){if(t!=null&&(t=""+gn(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+gn(n):""}function vd(e,t,n,i){if(t==null){if(i!=null){if(n!=null)throw Error(s(92));if(_e(i)){if(1<i.length)throw Error(s(93));i=i[0]}n=i}n==null&&(n=""),t=n}n=gn(t),e.defaultValue=n,i=e.textContent,i===n&&i!==""&&i!==null&&(e.value=i)}function qa(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var _g=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function bd(e,t,n){var i=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":i?e.setProperty(t,n):typeof n!="number"||n===0||_g.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Sd(e,t,n){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||t!=null&&t.hasOwnProperty(i)||(i.indexOf("--")===0?e.setProperty(i,""):i==="float"?e.cssFloat="":e[i]="");for(var u in t)i=t[u],t.hasOwnProperty(u)&&n[u]!==i&&bd(e,u,i)}else for(var c in t)t.hasOwnProperty(c)&&bd(e,c,t[c])}function Wu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Rg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ts(e){return Rg.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Iu=null;function eo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Va=null,Fa=null;function xd(e){var t=zl(e);if(t&&(e=t.stateNode)){var n=e[me]||null;e:switch(e=t.stateNode,t.type){case"input":if(Ju(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+vn(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var u=i[me]||null;if(!u)throw Error(s(90));Ju(i,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<n.length;t++)i=n[t],i.form===e.form&&pd(i)}break e;case"textarea":gd(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Ha(e,!!n.multiple,t,!1)}}}var to=!1;function Ed(e,t,n){if(to)return e(t,n);to=!0;try{var i=e(t);return i}finally{if(to=!1,(Va!==null||Fa!==null)&&(qs(),Va&&(t=Va,e=Fa,Fa=Va=null,xd(t),e)))for(t=0;t<e.length;t++)xd(e[t])}}function Li(e,t){var n=e.stateNode;if(n===null)return null;var i=n[me]||null;if(i===null)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var yl=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),no=!1;if(yl)try{var Bi={};Object.defineProperty(Bi,"passive",{get:function(){no=!0}}),window.addEventListener("test",Bi,Bi),window.removeEventListener("test",Bi,Bi)}catch{no=!1}var kl=null,lo=null,ns=null;function wd(){if(ns)return ns;var e,t=lo,n=t.length,i,u="value"in kl?kl.value:kl.textContent,c=u.length;for(e=0;e<n&&t[e]===u[e];e++);var y=n-e;for(i=1;i<=y&&t[n-i]===u[c-i];i++);return ns=u.slice(e,1<i?1-i:void 0)}function ls(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function as(){return!0}function Ad(){return!1}function Zt(e){function t(n,i,u,c,y){this._reactName=n,this._targetInst=u,this.type=i,this.nativeEvent=c,this.target=y,this.currentTarget=null;for(var b in e)e.hasOwnProperty(b)&&(n=e[b],this[b]=n?n(c):c[b]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?as:Ad,this.isPropagationStopped=Ad,this}return g(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=as)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=as)},persist:function(){},isPersistent:as}),t}var fa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},is=Zt(fa),ki=g({},fa,{view:0,detail:0}),Og=Zt(ki),ao,io,Hi,rs=g({},ki,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:so,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Hi&&(Hi&&e.type==="mousemove"?(ao=e.screenX-Hi.screenX,io=e.screenY-Hi.screenY):io=ao=0,Hi=e),ao)},movementY:function(e){return"movementY"in e?e.movementY:io}}),_d=Zt(rs),jg=g({},rs,{dataTransfer:0}),Dg=Zt(jg),Cg=g({},ki,{relatedTarget:0}),ro=Zt(Cg),Ng=g({},fa,{animationName:0,elapsedTime:0,pseudoElement:0}),Mg=Zt(Ng),Ug=g({},fa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),zg=Zt(Ug),Lg=g({},fa,{data:0}),Td=Zt(Lg),Bg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Hg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function qg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Hg[e])?!!t[e]:!1}function so(){return qg}var Vg=g({},ki,{key:function(e){if(e.key){var t=Bg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ls(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?kg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:so,charCode:function(e){return e.type==="keypress"?ls(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ls(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Fg=Zt(Vg),Yg=g({},rs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Rd=Zt(Yg),Gg=g({},ki,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:so}),$g=Zt(Gg),Xg=g({},fa,{propertyName:0,elapsedTime:0,pseudoElement:0}),Zg=Zt(Xg),Qg=g({},rs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Kg=Zt(Qg),Jg=g({},fa,{newState:0,oldState:0}),Pg=Zt(Jg),Wg=[9,13,27,32],uo=yl&&"CompositionEvent"in window,qi=null;yl&&"documentMode"in document&&(qi=document.documentMode);var Ig=yl&&"TextEvent"in window&&!qi,Od=yl&&(!uo||qi&&8<qi&&11>=qi),jd=" ",Dd=!1;function Cd(e,t){switch(e){case"keyup":return Wg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ya=!1;function ev(e,t){switch(e){case"compositionend":return Nd(t);case"keypress":return t.which!==32?null:(Dd=!0,jd);case"textInput":return e=t.data,e===jd&&Dd?null:e;default:return null}}function tv(e,t){if(Ya)return e==="compositionend"||!uo&&Cd(e,t)?(e=wd(),ns=lo=kl=null,Ya=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Od&&t.locale!=="ko"?null:t.data;default:return null}}var nv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Md(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!nv[e.type]:t==="textarea"}function Ud(e,t,n,i){Va?Fa?Fa.push(i):Fa=[i]:Va=i,t=Xs(t,"onChange"),0<t.length&&(n=new is("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var Vi=null,Fi=null;function lv(e){mp(e,0)}function ss(e){var t=ca(e);if(pd(t))return e}function zd(e,t){if(e==="change")return t}var Ld=!1;if(yl){var oo;if(yl){var co="oninput"in document;if(!co){var Bd=document.createElement("div");Bd.setAttribute("oninput","return;"),co=typeof Bd.oninput=="function"}oo=co}else oo=!1;Ld=oo&&(!document.documentMode||9<document.documentMode)}function kd(){Vi&&(Vi.detachEvent("onpropertychange",Hd),Fi=Vi=null)}function Hd(e){if(e.propertyName==="value"&&ss(Fi)){var t=[];Ud(t,Fi,e,eo(e)),Ed(lv,t)}}function av(e,t,n){e==="focusin"?(kd(),Vi=t,Fi=n,Vi.attachEvent("onpropertychange",Hd)):e==="focusout"&&kd()}function iv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ss(Fi)}function rv(e,t){if(e==="click")return ss(t)}function sv(e,t){if(e==="input"||e==="change")return ss(t)}function uv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var rn=typeof Object.is=="function"?Object.is:uv;function Yi(e,t){if(rn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var u=n[i];if(!mn.call(t,u)||!rn(e[u],t[u]))return!1}return!0}function qd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Vd(e,t){var n=qd(e);e=0;for(var i;n;){if(n.nodeType===3){if(i=e+n.textContent.length,e<=t&&i>=t)return{node:n,offset:t-e};e=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=qd(n)}}function Fd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Fd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Yd(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=es(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=es(e.document)}return t}function fo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var ov=yl&&"documentMode"in document&&11>=document.documentMode,Ga=null,ho=null,Gi=null,mo=!1;function Gd(e,t,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;mo||Ga==null||Ga!==es(i)||(i=Ga,"selectionStart"in i&&fo(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Gi&&Yi(Gi,i)||(Gi=i,i=Xs(ho,"onSelect"),0<i.length&&(t=new is("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=Ga)))}function da(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var $a={animationend:da("Animation","AnimationEnd"),animationiteration:da("Animation","AnimationIteration"),animationstart:da("Animation","AnimationStart"),transitionrun:da("Transition","TransitionRun"),transitionstart:da("Transition","TransitionStart"),transitioncancel:da("Transition","TransitionCancel"),transitionend:da("Transition","TransitionEnd")},po={},$d={};yl&&($d=document.createElement("div").style,"AnimationEvent"in window||(delete $a.animationend.animation,delete $a.animationiteration.animation,delete $a.animationstart.animation),"TransitionEvent"in window||delete $a.transitionend.transition);function ha(e){if(po[e])return po[e];if(!$a[e])return e;var t=$a[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in $d)return po[e]=t[n];return e}var Xd=ha("animationend"),Zd=ha("animationiteration"),Qd=ha("animationstart"),cv=ha("transitionrun"),fv=ha("transitionstart"),dv=ha("transitioncancel"),Kd=ha("transitionend"),Jd=new Map,yo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");yo.push("scrollEnd");function Cn(e,t){Jd.set(e,t),Xn(t,[e])}var Pd=new WeakMap;function bn(e,t){if(typeof e=="object"&&e!==null){var n=Pd.get(e);return n!==void 0?n:(t={value:e,source:t,stack:hd(t)},Pd.set(e,t),t)}return{value:e,source:t,stack:hd(t)}}var Sn=[],Xa=0,go=0;function us(){for(var e=Xa,t=go=Xa=0;t<e;){var n=Sn[t];Sn[t++]=null;var i=Sn[t];Sn[t++]=null;var u=Sn[t];Sn[t++]=null;var c=Sn[t];if(Sn[t++]=null,i!==null&&u!==null){var y=i.pending;y===null?u.next=u:(u.next=y.next,y.next=u),i.pending=u}c!==0&&Wd(n,u,c)}}function os(e,t,n,i){Sn[Xa++]=e,Sn[Xa++]=t,Sn[Xa++]=n,Sn[Xa++]=i,go|=i,e.lanes|=i,e=e.alternate,e!==null&&(e.lanes|=i)}function vo(e,t,n,i){return os(e,t,n,i),cs(e)}function Za(e,t){return os(e,null,null,t),cs(e)}function Wd(e,t,n){e.lanes|=n;var i=e.alternate;i!==null&&(i.lanes|=n);for(var u=!1,c=e.return;c!==null;)c.childLanes|=n,i=c.alternate,i!==null&&(i.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(u=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,u&&t!==null&&(u=31-ve(n),e=c.hiddenUpdates,i=e[u],i===null?e[u]=[t]:i.push(t),t.lane=n|536870912),c):null}function cs(e){if(50<pr)throw pr=0,Ac=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Qa={};function hv(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function sn(e,t,n,i){return new hv(e,t,n,i)}function bo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function gl(e,t){var n=e.alternate;return n===null?(n=sn(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Id(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function fs(e,t,n,i,u,c){var y=0;if(i=e,typeof e=="function")bo(e)&&(y=1);else if(typeof e=="string")y=pb(e,n,he.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case fe:return e=sn(31,n,t,u),e.elementType=fe,e.lanes=c,e;case E:return ma(n.children,u,c,t);case C:y=8,u|=24;break;case D:return e=sn(12,n,t,u|2),e.elementType=D,e.lanes=c,e;case V:return e=sn(13,n,t,u),e.elementType=V,e.lanes=c,e;case I:return e=sn(19,n,t,u),e.elementType=I,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case w:case M:y=10;break e;case T:y=9;break e;case X:y=11;break e;case W:y=14;break e;case se:y=16,i=null;break e}y=29,n=Error(s(130,e===null?"null":typeof e,"")),i=null}return t=sn(y,n,t,u),t.elementType=e,t.type=i,t.lanes=c,t}function ma(e,t,n,i){return e=sn(7,e,i,t),e.lanes=n,e}function So(e,t,n){return e=sn(6,e,null,t),e.lanes=n,e}function xo(e,t,n){return t=sn(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ka=[],Ja=0,ds=null,hs=0,xn=[],En=0,pa=null,vl=1,bl="";function ya(e,t){Ka[Ja++]=hs,Ka[Ja++]=ds,ds=e,hs=t}function eh(e,t,n){xn[En++]=vl,xn[En++]=bl,xn[En++]=pa,pa=e;var i=vl;e=bl;var u=32-ve(i)-1;i&=~(1<<u),n+=1;var c=32-ve(t)+u;if(30<c){var y=u-u%5;c=(i&(1<<y)-1).toString(32),i>>=y,u-=y,vl=1<<32-ve(t)+u|n<<u|i,bl=c+e}else vl=1<<c|n<<u|i,bl=e}function Eo(e){e.return!==null&&(ya(e,1),eh(e,1,0))}function wo(e){for(;e===ds;)ds=Ka[--Ja],Ka[Ja]=null,hs=Ka[--Ja],Ka[Ja]=null;for(;e===pa;)pa=xn[--En],xn[En]=null,bl=xn[--En],xn[En]=null,vl=xn[--En],xn[En]=null}var qt=null,it=null,He=!1,ga=null,Zn=!1,Ao=Error(s(519));function va(e){var t=Error(s(418,""));throw Zi(bn(t,e)),Ao}function th(e){var t=e.stateNode,n=e.type,i=e.memoizedProps;switch(t[ue]=e,t[me]=i,n){case"dialog":ze("cancel",t),ze("close",t);break;case"iframe":case"object":case"embed":ze("load",t);break;case"video":case"audio":for(n=0;n<gr.length;n++)ze(gr[n],t);break;case"source":ze("error",t);break;case"img":case"image":case"link":ze("error",t),ze("load",t);break;case"details":ze("toggle",t);break;case"input":ze("invalid",t),yd(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),Ir(t);break;case"select":ze("invalid",t);break;case"textarea":ze("invalid",t),vd(t,i.value,i.defaultValue,i.children),Ir(t)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||i.suppressHydrationWarning===!0||vp(t.textContent,n)?(i.popover!=null&&(ze("beforetoggle",t),ze("toggle",t)),i.onScroll!=null&&ze("scroll",t),i.onScrollEnd!=null&&ze("scrollend",t),i.onClick!=null&&(t.onclick=Zs),t=!0):t=!1,t||va(e)}function nh(e){for(qt=e.return;qt;)switch(qt.tag){case 5:case 13:Zn=!1;return;case 27:case 3:Zn=!0;return;default:qt=qt.return}}function $i(e){if(e!==qt)return!1;if(!He)return nh(e),He=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||qc(e.type,e.memoizedProps)),n=!n),n&&it&&va(e),nh(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){it=Mn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}it=null}}else t===27?(t=it,ea(e.type)?(e=Gc,Gc=null,it=e):it=t):it=qt?Mn(e.stateNode.nextSibling):null;return!0}function Xi(){it=qt=null,He=!1}function lh(){var e=ga;return e!==null&&(Jt===null?Jt=e:Jt.push.apply(Jt,e),ga=null),e}function Zi(e){ga===null?ga=[e]:ga.push(e)}var _o=Y(null),ba=null,Sl=null;function Hl(e,t,n){ee(_o,t._currentValue),t._currentValue=n}function xl(e){e._currentValue=_o.current,ae(_o)}function To(e,t,n){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function Ro(e,t,n,i){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var c=u.dependencies;if(c!==null){var y=u.child;c=c.firstContext;e:for(;c!==null;){var b=c;c=u;for(var A=0;A<t.length;A++)if(b.context===t[A]){c.lanes|=n,b=c.alternate,b!==null&&(b.lanes|=n),To(c.return,n,e),i||(y=null);break e}c=b.next}}else if(u.tag===18){if(y=u.return,y===null)throw Error(s(341));y.lanes|=n,c=y.alternate,c!==null&&(c.lanes|=n),To(y,n,e),y=null}else y=u.child;if(y!==null)y.return=u;else for(y=u;y!==null;){if(y===e){y=null;break}if(u=y.sibling,u!==null){u.return=y.return,y=u;break}y=y.return}u=y}}function Qi(e,t,n,i){e=null;for(var u=t,c=!1;u!==null;){if(!c){if((u.flags&524288)!==0)c=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var y=u.alternate;if(y===null)throw Error(s(387));if(y=y.memoizedProps,y!==null){var b=u.type;rn(u.pendingProps.value,y.value)||(e!==null?e.push(b):e=[b])}}else if(u===at.current){if(y=u.alternate,y===null)throw Error(s(387));y.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(wr):e=[wr])}u=u.return}e!==null&&Ro(t,e,n,i),t.flags|=262144}function ms(e){for(e=e.firstContext;e!==null;){if(!rn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Sa(e){ba=e,Sl=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function zt(e){return ah(ba,e)}function ps(e,t){return ba===null&&Sa(e),ah(e,t)}function ah(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},Sl===null){if(e===null)throw Error(s(308));Sl=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Sl=Sl.next=t;return n}var mv=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,i){e.push(i)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},pv=l.unstable_scheduleCallback,yv=l.unstable_NormalPriority,St={$$typeof:M,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Oo(){return{controller:new mv,data:new Map,refCount:0}}function Ki(e){e.refCount--,e.refCount===0&&pv(yv,function(){e.controller.abort()})}var Ji=null,jo=0,Pa=0,Wa=null;function gv(e,t){if(Ji===null){var n=Ji=[];jo=0,Pa=Cc(),Wa={status:"pending",value:void 0,then:function(i){n.push(i)}}}return jo++,t.then(ih,ih),t}function ih(){if(--jo===0&&Ji!==null){Wa!==null&&(Wa.status="fulfilled");var e=Ji;Ji=null,Pa=0,Wa=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function vv(e,t){var n=[],i={status:"pending",value:null,reason:null,then:function(u){n.push(u)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var u=0;u<n.length;u++)(0,n[u])(t)},function(u){for(i.status="rejected",i.reason=u,u=0;u<n.length;u++)(0,n[u])(void 0)}),i}var rh=H.S;H.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&gv(e,t),rh!==null&&rh(e,t)};var xa=Y(null);function Do(){var e=xa.current;return e!==null?e:Ie.pooledCache}function ys(e,t){t===null?ee(xa,xa.current):ee(xa,t.pool)}function sh(){var e=Do();return e===null?null:{parent:St._currentValue,pool:e}}var Pi=Error(s(460)),uh=Error(s(474)),gs=Error(s(542)),Co={then:function(){}};function oh(e){return e=e.status,e==="fulfilled"||e==="rejected"}function vs(){}function ch(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(vs,vs),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,dh(e),e;default:if(typeof t.status=="string")t.then(vs,vs);else{if(e=Ie,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(i){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=i}},function(i){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=i}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,dh(e),e}throw Wi=t,Pi}}var Wi=null;function fh(){if(Wi===null)throw Error(s(459));var e=Wi;return Wi=null,e}function dh(e){if(e===Pi||e===gs)throw Error(s(483))}var ql=!1;function No(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Mo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Vl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Fl(e,t,n){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Ve&2)!==0){var u=i.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),i.pending=t,t=cs(e),Wd(e,null,n),t}return os(e,i,t,n),cs(e)}function Ii(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,Ht(e,n)}}function Uo(e,t){var n=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var u=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var y={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?u=c=y:c=c.next=y,n=n.next}while(n!==null);c===null?u=c=t:c=c.next=t}else u=c=t;n={baseState:i.baseState,firstBaseUpdate:u,lastBaseUpdate:c,shared:i.shared,callbacks:i.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var zo=!1;function er(){if(zo){var e=Wa;if(e!==null)throw e}}function tr(e,t,n,i){zo=!1;var u=e.updateQueue;ql=!1;var c=u.firstBaseUpdate,y=u.lastBaseUpdate,b=u.shared.pending;if(b!==null){u.shared.pending=null;var A=b,L=A.next;A.next=null,y===null?c=L:y.next=L,y=A;var $=e.alternate;$!==null&&($=$.updateQueue,b=$.lastBaseUpdate,b!==y&&(b===null?$.firstBaseUpdate=L:b.next=L,$.lastBaseUpdate=A))}if(c!==null){var Q=u.baseState;y=0,$=L=A=null,b=c;do{var k=b.lane&-536870913,q=k!==b.lane;if(q?(Be&k)===k:(i&k)===k){k!==0&&k===Pa&&(zo=!0),$!==null&&($=$.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});e:{var Ae=e,Se=b;k=t;var Ze=n;switch(Se.tag){case 1:if(Ae=Se.payload,typeof Ae=="function"){Q=Ae.call(Ze,Q,k);break e}Q=Ae;break e;case 3:Ae.flags=Ae.flags&-65537|128;case 0:if(Ae=Se.payload,k=typeof Ae=="function"?Ae.call(Ze,Q,k):Ae,k==null)break e;Q=g({},Q,k);break e;case 2:ql=!0}}k=b.callback,k!==null&&(e.flags|=64,q&&(e.flags|=8192),q=u.callbacks,q===null?u.callbacks=[k]:q.push(k))}else q={lane:k,tag:b.tag,payload:b.payload,callback:b.callback,next:null},$===null?(L=$=q,A=Q):$=$.next=q,y|=k;if(b=b.next,b===null){if(b=u.shared.pending,b===null)break;q=b,b=q.next,q.next=null,u.lastBaseUpdate=q,u.shared.pending=null}}while(!0);$===null&&(A=Q),u.baseState=A,u.firstBaseUpdate=L,u.lastBaseUpdate=$,c===null&&(u.shared.lanes=0),Jl|=y,e.lanes=y,e.memoizedState=Q}}function hh(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function mh(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)hh(n[e],t)}var Ia=Y(null),bs=Y(0);function ph(e,t){e=Ol,ee(bs,e),ee(Ia,t),Ol=e|t.baseLanes}function Lo(){ee(bs,Ol),ee(Ia,Ia.current)}function Bo(){Ol=bs.current,ae(Ia),ae(bs)}var Yl=0,Ce=null,$e=null,yt=null,Ss=!1,ei=!1,Ea=!1,xs=0,nr=0,ti=null,bv=0;function ct(){throw Error(s(321))}function ko(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!rn(e[n],t[n]))return!1;return!0}function Ho(e,t,n,i,u,c){return Yl=c,Ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,H.H=e===null||e.memoizedState===null?Wh:Ih,Ea=!1,c=n(i,u),Ea=!1,ei&&(c=gh(t,n,i,u)),yh(e),c}function yh(e){H.H=Rs;var t=$e!==null&&$e.next!==null;if(Yl=0,yt=$e=Ce=null,Ss=!1,nr=0,ti=null,t)throw Error(s(300));e===null||Tt||(e=e.dependencies,e!==null&&ms(e)&&(Tt=!0))}function gh(e,t,n,i){Ce=e;var u=0;do{if(ei&&(ti=null),nr=0,ei=!1,25<=u)throw Error(s(301));if(u+=1,yt=$e=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}H.H=Tv,c=t(n,i)}while(ei);return c}function Sv(){var e=H.H,t=e.useState()[0];return t=typeof t.then=="function"?lr(t):t,e=e.useState()[0],($e!==null?$e.memoizedState:null)!==e&&(Ce.flags|=1024),t}function qo(){var e=xs!==0;return xs=0,e}function Vo(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Fo(e){if(Ss){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Ss=!1}Yl=0,yt=$e=Ce=null,ei=!1,nr=xs=0,ti=null}function Qt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return yt===null?Ce.memoizedState=yt=e:yt=yt.next=e,yt}function gt(){if($e===null){var e=Ce.alternate;e=e!==null?e.memoizedState:null}else e=$e.next;var t=yt===null?Ce.memoizedState:yt.next;if(t!==null)yt=t,$e=e;else{if(e===null)throw Ce.alternate===null?Error(s(467)):Error(s(310));$e=e,e={memoizedState:$e.memoizedState,baseState:$e.baseState,baseQueue:$e.baseQueue,queue:$e.queue,next:null},yt===null?Ce.memoizedState=yt=e:yt=yt.next=e}return yt}function Yo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function lr(e){var t=nr;return nr+=1,ti===null&&(ti=[]),e=ch(ti,e,t),t=Ce,(yt===null?t.memoizedState:yt.next)===null&&(t=t.alternate,H.H=t===null||t.memoizedState===null?Wh:Ih),e}function Es(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return lr(e);if(e.$$typeof===M)return zt(e)}throw Error(s(438,String(e)))}function Go(e){var t=null,n=Ce.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var i=Ce.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(t={data:i.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Yo(),Ce.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),i=0;i<e;i++)n[i]=De;return t.index++,n}function El(e,t){return typeof t=="function"?t(e):t}function ws(e){var t=gt();return $o(t,$e,e)}function $o(e,t,n){var i=e.queue;if(i===null)throw Error(s(311));i.lastRenderedReducer=n;var u=e.baseQueue,c=i.pending;if(c!==null){if(u!==null){var y=u.next;u.next=c.next,c.next=y}t.baseQueue=u=c,i.pending=null}if(c=e.baseState,u===null)e.memoizedState=c;else{t=u.next;var b=y=null,A=null,L=t,$=!1;do{var Q=L.lane&-536870913;if(Q!==L.lane?(Be&Q)===Q:(Yl&Q)===Q){var k=L.revertLane;if(k===0)A!==null&&(A=A.next={lane:0,revertLane:0,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null}),Q===Pa&&($=!0);else if((Yl&k)===k){L=L.next,k===Pa&&($=!0);continue}else Q={lane:0,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},A===null?(b=A=Q,y=c):A=A.next=Q,Ce.lanes|=k,Jl|=k;Q=L.action,Ea&&n(c,Q),c=L.hasEagerState?L.eagerState:n(c,Q)}else k={lane:Q,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},A===null?(b=A=k,y=c):A=A.next=k,Ce.lanes|=Q,Jl|=Q;L=L.next}while(L!==null&&L!==t);if(A===null?y=c:A.next=b,!rn(c,e.memoizedState)&&(Tt=!0,$&&(n=Wa,n!==null)))throw n;e.memoizedState=c,e.baseState=y,e.baseQueue=A,i.lastRenderedState=c}return u===null&&(i.lanes=0),[e.memoizedState,i.dispatch]}function Xo(e){var t=gt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var i=n.dispatch,u=n.pending,c=t.memoizedState;if(u!==null){n.pending=null;var y=u=u.next;do c=e(c,y.action),y=y.next;while(y!==u);rn(c,t.memoizedState)||(Tt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,i]}function vh(e,t,n){var i=Ce,u=gt(),c=He;if(c){if(n===void 0)throw Error(s(407));n=n()}else n=t();var y=!rn(($e||u).memoizedState,n);y&&(u.memoizedState=n,Tt=!0),u=u.queue;var b=xh.bind(null,i,u,e);if(ar(2048,8,b,[e]),u.getSnapshot!==t||y||yt!==null&&yt.memoizedState.tag&1){if(i.flags|=2048,ni(9,As(),Sh.bind(null,i,u,n,t),null),Ie===null)throw Error(s(349));c||(Yl&124)!==0||bh(i,t,n)}return n}function bh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Ce.updateQueue,t===null?(t=Yo(),Ce.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Sh(e,t,n,i){t.value=n,t.getSnapshot=i,Eh(t)&&wh(e)}function xh(e,t,n){return n(function(){Eh(t)&&wh(e)})}function Eh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!rn(e,n)}catch{return!0}}function wh(e){var t=Za(e,2);t!==null&&dn(t,e,2)}function Zo(e){var t=Qt();if(typeof e=="function"){var n=e;if(e=n(),Ea){ne(!0);try{n()}finally{ne(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:El,lastRenderedState:e},t}function Ah(e,t,n,i){return e.baseState=n,$o(e,$e,typeof i=="function"?i:El)}function xv(e,t,n,i,u){if(Ts(e))throw Error(s(485));if(e=t.action,e!==null){var c={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){c.listeners.push(y)}};H.T!==null?n(!0):c.isTransition=!1,i(c),n=t.pending,n===null?(c.next=t.pending=c,_h(t,c)):(c.next=n.next,t.pending=n.next=c)}}function _h(e,t){var n=t.action,i=t.payload,u=e.state;if(t.isTransition){var c=H.T,y={};H.T=y;try{var b=n(u,i),A=H.S;A!==null&&A(y,b),Th(e,t,b)}catch(L){Qo(e,t,L)}finally{H.T=c}}else try{c=n(u,i),Th(e,t,c)}catch(L){Qo(e,t,L)}}function Th(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){Rh(e,t,i)},function(i){return Qo(e,t,i)}):Rh(e,t,n)}function Rh(e,t,n){t.status="fulfilled",t.value=n,Oh(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,_h(e,n)))}function Qo(e,t,n){var i=e.pending;if(e.pending=null,i!==null){i=i.next;do t.status="rejected",t.reason=n,Oh(t),t=t.next;while(t!==i)}e.action=null}function Oh(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function jh(e,t){return t}function Dh(e,t){if(He){var n=Ie.formState;if(n!==null){e:{var i=Ce;if(He){if(it){t:{for(var u=it,c=Zn;u.nodeType!==8;){if(!c){u=null;break t}if(u=Mn(u.nextSibling),u===null){u=null;break t}}c=u.data,u=c==="F!"||c==="F"?u:null}if(u){it=Mn(u.nextSibling),i=u.data==="F!";break e}}va(i)}i=!1}i&&(t=n[0])}}return n=Qt(),n.memoizedState=n.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:jh,lastRenderedState:t},n.queue=i,n=Kh.bind(null,Ce,i),i.dispatch=n,i=Zo(!1),c=Io.bind(null,Ce,!1,i.queue),i=Qt(),u={state:t,dispatch:null,action:e,pending:null},i.queue=u,n=xv.bind(null,Ce,u,c,n),u.dispatch=n,i.memoizedState=e,[t,n,!1]}function Ch(e){var t=gt();return Nh(t,$e,e)}function Nh(e,t,n){if(t=$o(e,t,jh)[0],e=ws(El)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var i=lr(t)}catch(y){throw y===Pi?gs:y}else i=t;t=gt();var u=t.queue,c=u.dispatch;return n!==t.memoizedState&&(Ce.flags|=2048,ni(9,As(),Ev.bind(null,u,n),null)),[i,c,e]}function Ev(e,t){e.action=t}function Mh(e){var t=gt(),n=$e;if(n!==null)return Nh(t,n,e);gt(),t=t.memoizedState,n=gt();var i=n.queue.dispatch;return n.memoizedState=e,[t,i,!1]}function ni(e,t,n,i){return e={tag:e,create:n,deps:i,inst:t,next:null},t=Ce.updateQueue,t===null&&(t=Yo(),Ce.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function As(){return{destroy:void 0,resource:void 0}}function Uh(){return gt().memoizedState}function _s(e,t,n,i){var u=Qt();i=i===void 0?null:i,Ce.flags|=e,u.memoizedState=ni(1|t,As(),n,i)}function ar(e,t,n,i){var u=gt();i=i===void 0?null:i;var c=u.memoizedState.inst;$e!==null&&i!==null&&ko(i,$e.memoizedState.deps)?u.memoizedState=ni(t,c,n,i):(Ce.flags|=e,u.memoizedState=ni(1|t,c,n,i))}function zh(e,t){_s(8390656,8,e,t)}function Lh(e,t){ar(2048,8,e,t)}function Bh(e,t){return ar(4,2,e,t)}function kh(e,t){return ar(4,4,e,t)}function Hh(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function qh(e,t,n){n=n!=null?n.concat([e]):null,ar(4,4,Hh.bind(null,t,e),n)}function Ko(){}function Vh(e,t){var n=gt();t=t===void 0?null:t;var i=n.memoizedState;return t!==null&&ko(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function Fh(e,t){var n=gt();t=t===void 0?null:t;var i=n.memoizedState;if(t!==null&&ko(t,i[1]))return i[0];if(i=e(),Ea){ne(!0);try{e()}finally{ne(!1)}}return n.memoizedState=[i,t],i}function Jo(e,t,n){return n===void 0||(Yl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=$m(),Ce.lanes|=e,Jl|=e,n)}function Yh(e,t,n,i){return rn(n,t)?n:Ia.current!==null?(e=Jo(e,n,i),rn(e,t)||(Tt=!0),e):(Yl&42)===0?(Tt=!0,e.memoizedState=n):(e=$m(),Ce.lanes|=e,Jl|=e,t)}function Gh(e,t,n,i,u){var c=J.p;J.p=c!==0&&8>c?c:8;var y=H.T,b={};H.T=b,Io(e,!1,t,n);try{var A=u(),L=H.S;if(L!==null&&L(b,A),A!==null&&typeof A=="object"&&typeof A.then=="function"){var $=vv(A,i);ir(e,t,$,fn(e))}else ir(e,t,i,fn(e))}catch(Q){ir(e,t,{then:function(){},status:"rejected",reason:Q},fn())}finally{J.p=c,H.T=y}}function wv(){}function Po(e,t,n,i){if(e.tag!==5)throw Error(s(476));var u=$h(e).queue;Gh(e,u,t,te,n===null?wv:function(){return Xh(e),n(i)})}function $h(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:te,baseState:te,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:El,lastRenderedState:te},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:El,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Xh(e){var t=$h(e).next.queue;ir(e,t,{},fn())}function Wo(){return zt(wr)}function Zh(){return gt().memoizedState}function Qh(){return gt().memoizedState}function Av(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=fn();e=Vl(n);var i=Fl(t,e,n);i!==null&&(dn(i,t,n),Ii(i,t,n)),t={cache:Oo()},e.payload=t;return}t=t.return}}function _v(e,t,n){var i=fn();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ts(e)?Jh(t,n):(n=vo(e,t,n,i),n!==null&&(dn(n,e,i),Ph(n,t,i)))}function Kh(e,t,n){var i=fn();ir(e,t,n,i)}function ir(e,t,n,i){var u={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ts(e))Jh(t,u);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var y=t.lastRenderedState,b=c(y,n);if(u.hasEagerState=!0,u.eagerState=b,rn(b,y))return os(e,t,u,0),Ie===null&&us(),!1}catch{}finally{}if(n=vo(e,t,u,i),n!==null)return dn(n,e,i),Ph(n,t,i),!0}return!1}function Io(e,t,n,i){if(i={lane:2,revertLane:Cc(),action:i,hasEagerState:!1,eagerState:null,next:null},Ts(e)){if(t)throw Error(s(479))}else t=vo(e,n,i,2),t!==null&&dn(t,e,2)}function Ts(e){var t=e.alternate;return e===Ce||t!==null&&t===Ce}function Jh(e,t){ei=Ss=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ph(e,t,n){if((n&4194048)!==0){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,Ht(e,n)}}var Rs={readContext:zt,use:Es,useCallback:ct,useContext:ct,useEffect:ct,useImperativeHandle:ct,useLayoutEffect:ct,useInsertionEffect:ct,useMemo:ct,useReducer:ct,useRef:ct,useState:ct,useDebugValue:ct,useDeferredValue:ct,useTransition:ct,useSyncExternalStore:ct,useId:ct,useHostTransitionStatus:ct,useFormState:ct,useActionState:ct,useOptimistic:ct,useMemoCache:ct,useCacheRefresh:ct},Wh={readContext:zt,use:Es,useCallback:function(e,t){return Qt().memoizedState=[e,t===void 0?null:t],e},useContext:zt,useEffect:zh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,_s(4194308,4,Hh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return _s(4194308,4,e,t)},useInsertionEffect:function(e,t){_s(4,2,e,t)},useMemo:function(e,t){var n=Qt();t=t===void 0?null:t;var i=e();if(Ea){ne(!0);try{e()}finally{ne(!1)}}return n.memoizedState=[i,t],i},useReducer:function(e,t,n){var i=Qt();if(n!==void 0){var u=n(t);if(Ea){ne(!0);try{n(t)}finally{ne(!1)}}}else u=t;return i.memoizedState=i.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},i.queue=e,e=e.dispatch=_v.bind(null,Ce,e),[i.memoizedState,e]},useRef:function(e){var t=Qt();return e={current:e},t.memoizedState=e},useState:function(e){e=Zo(e);var t=e.queue,n=Kh.bind(null,Ce,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Ko,useDeferredValue:function(e,t){var n=Qt();return Jo(n,e,t)},useTransition:function(){var e=Zo(!1);return e=Gh.bind(null,Ce,e.queue,!0,!1),Qt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=Ce,u=Qt();if(He){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),Ie===null)throw Error(s(349));(Be&124)!==0||bh(i,t,n)}u.memoizedState=n;var c={value:n,getSnapshot:t};return u.queue=c,zh(xh.bind(null,i,c,e),[e]),i.flags|=2048,ni(9,As(),Sh.bind(null,i,c,n,t),null),n},useId:function(){var e=Qt(),t=Ie.identifierPrefix;if(He){var n=bl,i=vl;n=(i&~(1<<32-ve(i)-1)).toString(32)+n,t="«"+t+"R"+n,n=xs++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=bv++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Wo,useFormState:Dh,useActionState:Dh,useOptimistic:function(e){var t=Qt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Io.bind(null,Ce,!0,n),n.dispatch=t,[e,t]},useMemoCache:Go,useCacheRefresh:function(){return Qt().memoizedState=Av.bind(null,Ce)}},Ih={readContext:zt,use:Es,useCallback:Vh,useContext:zt,useEffect:Lh,useImperativeHandle:qh,useInsertionEffect:Bh,useLayoutEffect:kh,useMemo:Fh,useReducer:ws,useRef:Uh,useState:function(){return ws(El)},useDebugValue:Ko,useDeferredValue:function(e,t){var n=gt();return Yh(n,$e.memoizedState,e,t)},useTransition:function(){var e=ws(El)[0],t=gt().memoizedState;return[typeof e=="boolean"?e:lr(e),t]},useSyncExternalStore:vh,useId:Zh,useHostTransitionStatus:Wo,useFormState:Ch,useActionState:Ch,useOptimistic:function(e,t){var n=gt();return Ah(n,$e,e,t)},useMemoCache:Go,useCacheRefresh:Qh},Tv={readContext:zt,use:Es,useCallback:Vh,useContext:zt,useEffect:Lh,useImperativeHandle:qh,useInsertionEffect:Bh,useLayoutEffect:kh,useMemo:Fh,useReducer:Xo,useRef:Uh,useState:function(){return Xo(El)},useDebugValue:Ko,useDeferredValue:function(e,t){var n=gt();return $e===null?Jo(n,e,t):Yh(n,$e.memoizedState,e,t)},useTransition:function(){var e=Xo(El)[0],t=gt().memoizedState;return[typeof e=="boolean"?e:lr(e),t]},useSyncExternalStore:vh,useId:Zh,useHostTransitionStatus:Wo,useFormState:Mh,useActionState:Mh,useOptimistic:function(e,t){var n=gt();return $e!==null?Ah(n,$e,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Go,useCacheRefresh:Qh},li=null,rr=0;function Os(e){var t=rr;return rr+=1,li===null&&(li=[]),ch(li,e,t)}function sr(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function js(e,t){throw t.$$typeof===S?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function em(e){var t=e._init;return t(e._payload)}function tm(e){function t(U,N){if(e){var z=U.deletions;z===null?(U.deletions=[N],U.flags|=16):z.push(N)}}function n(U,N){if(!e)return null;for(;N!==null;)t(U,N),N=N.sibling;return null}function i(U){for(var N=new Map;U!==null;)U.key!==null?N.set(U.key,U):N.set(U.index,U),U=U.sibling;return N}function u(U,N){return U=gl(U,N),U.index=0,U.sibling=null,U}function c(U,N,z){return U.index=z,e?(z=U.alternate,z!==null?(z=z.index,z<N?(U.flags|=67108866,N):z):(U.flags|=67108866,N)):(U.flags|=1048576,N)}function y(U){return e&&U.alternate===null&&(U.flags|=67108866),U}function b(U,N,z,Z){return N===null||N.tag!==6?(N=So(z,U.mode,Z),N.return=U,N):(N=u(N,z),N.return=U,N)}function A(U,N,z,Z){var oe=z.type;return oe===E?$(U,N,z.props.children,Z,z.key):N!==null&&(N.elementType===oe||typeof oe=="object"&&oe!==null&&oe.$$typeof===se&&em(oe)===N.type)?(N=u(N,z.props),sr(N,z),N.return=U,N):(N=fs(z.type,z.key,z.props,null,U.mode,Z),sr(N,z),N.return=U,N)}function L(U,N,z,Z){return N===null||N.tag!==4||N.stateNode.containerInfo!==z.containerInfo||N.stateNode.implementation!==z.implementation?(N=xo(z,U.mode,Z),N.return=U,N):(N=u(N,z.children||[]),N.return=U,N)}function $(U,N,z,Z,oe){return N===null||N.tag!==7?(N=ma(z,U.mode,Z,oe),N.return=U,N):(N=u(N,z),N.return=U,N)}function Q(U,N,z){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return N=So(""+N,U.mode,z),N.return=U,N;if(typeof N=="object"&&N!==null){switch(N.$$typeof){case x:return z=fs(N.type,N.key,N.props,null,U.mode,z),sr(z,N),z.return=U,z;case R:return N=xo(N,U.mode,z),N.return=U,N;case se:var Z=N._init;return N=Z(N._payload),Q(U,N,z)}if(_e(N)||re(N))return N=ma(N,U.mode,z,null),N.return=U,N;if(typeof N.then=="function")return Q(U,Os(N),z);if(N.$$typeof===M)return Q(U,ps(U,N),z);js(U,N)}return null}function k(U,N,z,Z){var oe=N!==null?N.key:null;if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return oe!==null?null:b(U,N,""+z,Z);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case x:return z.key===oe?A(U,N,z,Z):null;case R:return z.key===oe?L(U,N,z,Z):null;case se:return oe=z._init,z=oe(z._payload),k(U,N,z,Z)}if(_e(z)||re(z))return oe!==null?null:$(U,N,z,Z,null);if(typeof z.then=="function")return k(U,N,Os(z),Z);if(z.$$typeof===M)return k(U,N,ps(U,z),Z);js(U,z)}return null}function q(U,N,z,Z,oe){if(typeof Z=="string"&&Z!==""||typeof Z=="number"||typeof Z=="bigint")return U=U.get(z)||null,b(N,U,""+Z,oe);if(typeof Z=="object"&&Z!==null){switch(Z.$$typeof){case x:return U=U.get(Z.key===null?z:Z.key)||null,A(N,U,Z,oe);case R:return U=U.get(Z.key===null?z:Z.key)||null,L(N,U,Z,oe);case se:var Me=Z._init;return Z=Me(Z._payload),q(U,N,z,Z,oe)}if(_e(Z)||re(Z))return U=U.get(z)||null,$(N,U,Z,oe,null);if(typeof Z.then=="function")return q(U,N,z,Os(Z),oe);if(Z.$$typeof===M)return q(U,N,z,ps(N,Z),oe);js(N,Z)}return null}function Ae(U,N,z,Z){for(var oe=null,Me=null,pe=N,Ee=N=0,Ot=null;pe!==null&&Ee<z.length;Ee++){pe.index>Ee?(Ot=pe,pe=null):Ot=pe.sibling;var ke=k(U,pe,z[Ee],Z);if(ke===null){pe===null&&(pe=Ot);break}e&&pe&&ke.alternate===null&&t(U,pe),N=c(ke,N,Ee),Me===null?oe=ke:Me.sibling=ke,Me=ke,pe=Ot}if(Ee===z.length)return n(U,pe),He&&ya(U,Ee),oe;if(pe===null){for(;Ee<z.length;Ee++)pe=Q(U,z[Ee],Z),pe!==null&&(N=c(pe,N,Ee),Me===null?oe=pe:Me.sibling=pe,Me=pe);return He&&ya(U,Ee),oe}for(pe=i(pe);Ee<z.length;Ee++)Ot=q(pe,U,Ee,z[Ee],Z),Ot!==null&&(e&&Ot.alternate!==null&&pe.delete(Ot.key===null?Ee:Ot.key),N=c(Ot,N,Ee),Me===null?oe=Ot:Me.sibling=Ot,Me=Ot);return e&&pe.forEach(function(ia){return t(U,ia)}),He&&ya(U,Ee),oe}function Se(U,N,z,Z){if(z==null)throw Error(s(151));for(var oe=null,Me=null,pe=N,Ee=N=0,Ot=null,ke=z.next();pe!==null&&!ke.done;Ee++,ke=z.next()){pe.index>Ee?(Ot=pe,pe=null):Ot=pe.sibling;var ia=k(U,pe,ke.value,Z);if(ia===null){pe===null&&(pe=Ot);break}e&&pe&&ia.alternate===null&&t(U,pe),N=c(ia,N,Ee),Me===null?oe=ia:Me.sibling=ia,Me=ia,pe=Ot}if(ke.done)return n(U,pe),He&&ya(U,Ee),oe;if(pe===null){for(;!ke.done;Ee++,ke=z.next())ke=Q(U,ke.value,Z),ke!==null&&(N=c(ke,N,Ee),Me===null?oe=ke:Me.sibling=ke,Me=ke);return He&&ya(U,Ee),oe}for(pe=i(pe);!ke.done;Ee++,ke=z.next())ke=q(pe,U,Ee,ke.value,Z),ke!==null&&(e&&ke.alternate!==null&&pe.delete(ke.key===null?Ee:ke.key),N=c(ke,N,Ee),Me===null?oe=ke:Me.sibling=ke,Me=ke);return e&&pe.forEach(function(Rb){return t(U,Rb)}),He&&ya(U,Ee),oe}function Ze(U,N,z,Z){if(typeof z=="object"&&z!==null&&z.type===E&&z.key===null&&(z=z.props.children),typeof z=="object"&&z!==null){switch(z.$$typeof){case x:e:{for(var oe=z.key;N!==null;){if(N.key===oe){if(oe=z.type,oe===E){if(N.tag===7){n(U,N.sibling),Z=u(N,z.props.children),Z.return=U,U=Z;break e}}else if(N.elementType===oe||typeof oe=="object"&&oe!==null&&oe.$$typeof===se&&em(oe)===N.type){n(U,N.sibling),Z=u(N,z.props),sr(Z,z),Z.return=U,U=Z;break e}n(U,N);break}else t(U,N);N=N.sibling}z.type===E?(Z=ma(z.props.children,U.mode,Z,z.key),Z.return=U,U=Z):(Z=fs(z.type,z.key,z.props,null,U.mode,Z),sr(Z,z),Z.return=U,U=Z)}return y(U);case R:e:{for(oe=z.key;N!==null;){if(N.key===oe)if(N.tag===4&&N.stateNode.containerInfo===z.containerInfo&&N.stateNode.implementation===z.implementation){n(U,N.sibling),Z=u(N,z.children||[]),Z.return=U,U=Z;break e}else{n(U,N);break}else t(U,N);N=N.sibling}Z=xo(z,U.mode,Z),Z.return=U,U=Z}return y(U);case se:return oe=z._init,z=oe(z._payload),Ze(U,N,z,Z)}if(_e(z))return Ae(U,N,z,Z);if(re(z)){if(oe=re(z),typeof oe!="function")throw Error(s(150));return z=oe.call(z),Se(U,N,z,Z)}if(typeof z.then=="function")return Ze(U,N,Os(z),Z);if(z.$$typeof===M)return Ze(U,N,ps(U,z),Z);js(U,z)}return typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint"?(z=""+z,N!==null&&N.tag===6?(n(U,N.sibling),Z=u(N,z),Z.return=U,U=Z):(n(U,N),Z=So(z,U.mode,Z),Z.return=U,U=Z),y(U)):n(U,N)}return function(U,N,z,Z){try{rr=0;var oe=Ze(U,N,z,Z);return li=null,oe}catch(pe){if(pe===Pi||pe===gs)throw pe;var Me=sn(29,pe,null,U.mode);return Me.lanes=Z,Me.return=U,Me}finally{}}}var ai=tm(!0),nm=tm(!1),wn=Y(null),Qn=null;function Gl(e){var t=e.alternate;ee(xt,xt.current&1),ee(wn,e),Qn===null&&(t===null||Ia.current!==null||t.memoizedState!==null)&&(Qn=e)}function lm(e){if(e.tag===22){if(ee(xt,xt.current),ee(wn,e),Qn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Qn=e)}}else $l()}function $l(){ee(xt,xt.current),ee(wn,wn.current)}function wl(e){ae(wn),Qn===e&&(Qn=null),ae(xt)}var xt=Y(0);function Ds(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Yc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function ec(e,t,n,i){t=e.memoizedState,n=n(i,t),n=n==null?t:g({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var tc={enqueueSetState:function(e,t,n){e=e._reactInternals;var i=fn(),u=Vl(i);u.payload=t,n!=null&&(u.callback=n),t=Fl(e,u,i),t!==null&&(dn(t,e,i),Ii(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=fn(),u=Vl(i);u.tag=1,u.payload=t,n!=null&&(u.callback=n),t=Fl(e,u,i),t!==null&&(dn(t,e,i),Ii(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=fn(),i=Vl(n);i.tag=2,t!=null&&(i.callback=t),t=Fl(e,i,n),t!==null&&(dn(t,e,n),Ii(t,e,n))}};function am(e,t,n,i,u,c,y){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,c,y):t.prototype&&t.prototype.isPureReactComponent?!Yi(n,i)||!Yi(u,c):!0}function im(e,t,n,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&tc.enqueueReplaceState(t,t.state,null)}function wa(e,t){var n=t;if("ref"in t){n={};for(var i in t)i!=="ref"&&(n[i]=t[i])}if(e=e.defaultProps){n===t&&(n=g({},n));for(var u in e)n[u]===void 0&&(n[u]=e[u])}return n}var Cs=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function rm(e){Cs(e)}function sm(e){console.error(e)}function um(e){Cs(e)}function Ns(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(i){setTimeout(function(){throw i})}}function om(e,t,n){try{var i=e.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function nc(e,t,n){return n=Vl(n),n.tag=3,n.payload={element:null},n.callback=function(){Ns(e,t)},n}function cm(e){return e=Vl(e),e.tag=3,e}function fm(e,t,n,i){var u=n.type.getDerivedStateFromError;if(typeof u=="function"){var c=i.value;e.payload=function(){return u(c)},e.callback=function(){om(t,n,i)}}var y=n.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(e.callback=function(){om(t,n,i),typeof u!="function"&&(Pl===null?Pl=new Set([this]):Pl.add(this));var b=i.stack;this.componentDidCatch(i.value,{componentStack:b!==null?b:""})})}function Rv(e,t,n,i,u){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(t=n.alternate,t!==null&&Qi(t,n,u,!0),n=wn.current,n!==null){switch(n.tag){case 13:return Qn===null?Tc():n.alternate===null&&rt===0&&(rt=3),n.flags&=-257,n.flags|=65536,n.lanes=u,i===Co?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([i]):t.add(i),Oc(e,i,u)),!1;case 22:return n.flags|=65536,i===Co?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([i]):n.add(i)),Oc(e,i,u)),!1}throw Error(s(435,n.tag))}return Oc(e,i,u),Tc(),!1}if(He)return t=wn.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,i!==Ao&&(e=Error(s(422),{cause:i}),Zi(bn(e,n)))):(i!==Ao&&(t=Error(s(423),{cause:i}),Zi(bn(t,n))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,i=bn(i,n),u=nc(e.stateNode,i,u),Uo(e,u),rt!==4&&(rt=2)),!1;var c=Error(s(520),{cause:i});if(c=bn(c,n),mr===null?mr=[c]:mr.push(c),rt!==4&&(rt=2),t===null)return!0;i=bn(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=u&-u,n.lanes|=e,e=nc(n.stateNode,i,e),Uo(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Pl===null||!Pl.has(c))))return n.flags|=65536,u&=-u,n.lanes|=u,u=cm(u),fm(u,e,n,i),Uo(n,u),!1}n=n.return}while(n!==null);return!1}var dm=Error(s(461)),Tt=!1;function Ct(e,t,n,i){t.child=e===null?nm(t,null,n,i):ai(t,e.child,n,i)}function hm(e,t,n,i,u){n=n.render;var c=t.ref;if("ref"in i){var y={};for(var b in i)b!=="ref"&&(y[b]=i[b])}else y=i;return Sa(t),i=Ho(e,t,n,y,c,u),b=qo(),e!==null&&!Tt?(Vo(e,t,u),Al(e,t,u)):(He&&b&&Eo(t),t.flags|=1,Ct(e,t,i,u),t.child)}function mm(e,t,n,i,u){if(e===null){var c=n.type;return typeof c=="function"&&!bo(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,pm(e,t,c,i,u)):(e=fs(n.type,null,i,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!cc(e,u)){var y=c.memoizedProps;if(n=n.compare,n=n!==null?n:Yi,n(y,i)&&e.ref===t.ref)return Al(e,t,u)}return t.flags|=1,e=gl(c,i),e.ref=t.ref,e.return=t,t.child=e}function pm(e,t,n,i,u){if(e!==null){var c=e.memoizedProps;if(Yi(c,i)&&e.ref===t.ref)if(Tt=!1,t.pendingProps=i=c,cc(e,u))(e.flags&131072)!==0&&(Tt=!0);else return t.lanes=e.lanes,Al(e,t,u)}return lc(e,t,n,i,u)}function ym(e,t,n){var i=t.pendingProps,u=i.children,c=e!==null?e.memoizedState:null;if(i.mode==="hidden"){if((t.flags&128)!==0){if(i=c!==null?c.baseLanes|n:n,e!==null){for(u=t.child=e.child,c=0;u!==null;)c=c|u.lanes|u.childLanes,u=u.sibling;t.childLanes=c&~i}else t.childLanes=0,t.child=null;return gm(e,t,i,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&ys(t,c!==null?c.cachePool:null),c!==null?ph(t,c):Lo(),lm(t);else return t.lanes=t.childLanes=536870912,gm(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(ys(t,c.cachePool),ph(t,c),$l(),t.memoizedState=null):(e!==null&&ys(t,null),Lo(),$l());return Ct(e,t,u,n),t.child}function gm(e,t,n,i){var u=Do();return u=u===null?null:{parent:St._currentValue,pool:u},t.memoizedState={baseLanes:n,cachePool:u},e!==null&&ys(t,null),Lo(),lm(t),e!==null&&Qi(e,t,i,!0),null}function Ms(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(s(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function lc(e,t,n,i,u){return Sa(t),n=Ho(e,t,n,i,void 0,u),i=qo(),e!==null&&!Tt?(Vo(e,t,u),Al(e,t,u)):(He&&i&&Eo(t),t.flags|=1,Ct(e,t,n,u),t.child)}function vm(e,t,n,i,u,c){return Sa(t),t.updateQueue=null,n=gh(t,i,n,u),yh(e),i=qo(),e!==null&&!Tt?(Vo(e,t,c),Al(e,t,c)):(He&&i&&Eo(t),t.flags|=1,Ct(e,t,n,c),t.child)}function bm(e,t,n,i,u){if(Sa(t),t.stateNode===null){var c=Qa,y=n.contextType;typeof y=="object"&&y!==null&&(c=zt(y)),c=new n(i,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=tc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=i,c.state=t.memoizedState,c.refs={},No(t),y=n.contextType,c.context=typeof y=="object"&&y!==null?zt(y):Qa,c.state=t.memoizedState,y=n.getDerivedStateFromProps,typeof y=="function"&&(ec(t,n,y,i),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(y=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),y!==c.state&&tc.enqueueReplaceState(c,c.state,null),tr(t,i,c,u),er(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!0}else if(e===null){c=t.stateNode;var b=t.memoizedProps,A=wa(n,b);c.props=A;var L=c.context,$=n.contextType;y=Qa,typeof $=="object"&&$!==null&&(y=zt($));var Q=n.getDerivedStateFromProps;$=typeof Q=="function"||typeof c.getSnapshotBeforeUpdate=="function",b=t.pendingProps!==b,$||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(b||L!==y)&&im(t,c,i,y),ql=!1;var k=t.memoizedState;c.state=k,tr(t,i,c,u),er(),L=t.memoizedState,b||k!==L||ql?(typeof Q=="function"&&(ec(t,n,Q,i),L=t.memoizedState),(A=ql||am(t,n,A,i,k,L,y))?($||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=L),c.props=i,c.state=L,c.context=y,i=A):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{c=t.stateNode,Mo(e,t),y=t.memoizedProps,$=wa(n,y),c.props=$,Q=t.pendingProps,k=c.context,L=n.contextType,A=Qa,typeof L=="object"&&L!==null&&(A=zt(L)),b=n.getDerivedStateFromProps,(L=typeof b=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y!==Q||k!==A)&&im(t,c,i,A),ql=!1,k=t.memoizedState,c.state=k,tr(t,i,c,u),er();var q=t.memoizedState;y!==Q||k!==q||ql||e!==null&&e.dependencies!==null&&ms(e.dependencies)?(typeof b=="function"&&(ec(t,n,b,i),q=t.memoizedState),($=ql||am(t,n,$,i,k,q,A)||e!==null&&e.dependencies!==null&&ms(e.dependencies))?(L||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(i,q,A),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(i,q,A)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||y===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=q),c.props=i,c.state=q,c.context=A,i=$):(typeof c.componentDidUpdate!="function"||y===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),i=!1)}return c=i,Ms(e,t),i=(t.flags&128)!==0,c||i?(c=t.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&i?(t.child=ai(t,e.child,null,u),t.child=ai(t,null,n,u)):Ct(e,t,n,u),t.memoizedState=c.state,e=t.child):e=Al(e,t,u),e}function Sm(e,t,n,i){return Xi(),t.flags|=256,Ct(e,t,n,i),t.child}var ac={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ic(e){return{baseLanes:e,cachePool:sh()}}function rc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=An),e}function xm(e,t,n){var i=t.pendingProps,u=!1,c=(t.flags&128)!==0,y;if((y=c)||(y=e!==null&&e.memoizedState===null?!1:(xt.current&2)!==0),y&&(u=!0,t.flags&=-129),y=(t.flags&32)!==0,t.flags&=-33,e===null){if(He){if(u?Gl(t):$l(),He){var b=it,A;if(A=b){e:{for(A=b,b=Zn;A.nodeType!==8;){if(!b){b=null;break e}if(A=Mn(A.nextSibling),A===null){b=null;break e}}b=A}b!==null?(t.memoizedState={dehydrated:b,treeContext:pa!==null?{id:vl,overflow:bl}:null,retryLane:536870912,hydrationErrors:null},A=sn(18,null,null,0),A.stateNode=b,A.return=t,t.child=A,qt=t,it=null,A=!0):A=!1}A||va(t)}if(b=t.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return Yc(b)?t.lanes=32:t.lanes=536870912,null;wl(t)}return b=i.children,i=i.fallback,u?($l(),u=t.mode,b=Us({mode:"hidden",children:b},u),i=ma(i,u,n,null),b.return=t,i.return=t,b.sibling=i,t.child=b,u=t.child,u.memoizedState=ic(n),u.childLanes=rc(e,y,n),t.memoizedState=ac,i):(Gl(t),sc(t,b))}if(A=e.memoizedState,A!==null&&(b=A.dehydrated,b!==null)){if(c)t.flags&256?(Gl(t),t.flags&=-257,t=uc(e,t,n)):t.memoizedState!==null?($l(),t.child=e.child,t.flags|=128,t=null):($l(),u=i.fallback,b=t.mode,i=Us({mode:"visible",children:i.children},b),u=ma(u,b,n,null),u.flags|=2,i.return=t,u.return=t,i.sibling=u,t.child=i,ai(t,e.child,null,n),i=t.child,i.memoizedState=ic(n),i.childLanes=rc(e,y,n),t.memoizedState=ac,t=u);else if(Gl(t),Yc(b)){if(y=b.nextSibling&&b.nextSibling.dataset,y)var L=y.dgst;y=L,i=Error(s(419)),i.stack="",i.digest=y,Zi({value:i,source:null,stack:null}),t=uc(e,t,n)}else if(Tt||Qi(e,t,n,!1),y=(n&e.childLanes)!==0,Tt||y){if(y=Ie,y!==null&&(i=n&-n,i=(i&42)!==0?1:be(i),i=(i&(y.suspendedLanes|n))!==0?0:i,i!==0&&i!==A.retryLane))throw A.retryLane=i,Za(e,i),dn(y,e,i),dm;b.data==="$?"||Tc(),t=uc(e,t,n)}else b.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=A.treeContext,it=Mn(b.nextSibling),qt=t,He=!0,ga=null,Zn=!1,e!==null&&(xn[En++]=vl,xn[En++]=bl,xn[En++]=pa,vl=e.id,bl=e.overflow,pa=t),t=sc(t,i.children),t.flags|=4096);return t}return u?($l(),u=i.fallback,b=t.mode,A=e.child,L=A.sibling,i=gl(A,{mode:"hidden",children:i.children}),i.subtreeFlags=A.subtreeFlags&65011712,L!==null?u=gl(L,u):(u=ma(u,b,n,null),u.flags|=2),u.return=t,i.return=t,i.sibling=u,t.child=i,i=u,u=t.child,b=e.child.memoizedState,b===null?b=ic(n):(A=b.cachePool,A!==null?(L=St._currentValue,A=A.parent!==L?{parent:L,pool:L}:A):A=sh(),b={baseLanes:b.baseLanes|n,cachePool:A}),u.memoizedState=b,u.childLanes=rc(e,y,n),t.memoizedState=ac,i):(Gl(t),n=e.child,e=n.sibling,n=gl(n,{mode:"visible",children:i.children}),n.return=t,n.sibling=null,e!==null&&(y=t.deletions,y===null?(t.deletions=[e],t.flags|=16):y.push(e)),t.child=n,t.memoizedState=null,n)}function sc(e,t){return t=Us({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Us(e,t){return e=sn(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function uc(e,t,n){return ai(t,e.child,null,n),e=sc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Em(e,t,n){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),To(e.return,t,n)}function oc(e,t,n,i,u){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:u}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=i,c.tail=n,c.tailMode=u)}function wm(e,t,n){var i=t.pendingProps,u=i.revealOrder,c=i.tail;if(Ct(e,t,i.children,n),i=xt.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Em(e,n,t);else if(e.tag===19)Em(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(ee(xt,i),u){case"forwards":for(n=t.child,u=null;n!==null;)e=n.alternate,e!==null&&Ds(e)===null&&(u=n),n=n.sibling;n=u,n===null?(u=t.child,t.child=null):(u=n.sibling,n.sibling=null),oc(t,!1,u,n,c);break;case"backwards":for(n=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Ds(e)===null){t.child=u;break}e=u.sibling,u.sibling=n,n=u,u=e}oc(t,!0,n,null,c);break;case"together":oc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Al(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Jl|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Qi(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=gl(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=gl(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function cc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ms(e)))}function Ov(e,t,n){switch(t.tag){case 3:Te(t,t.stateNode.containerInfo),Hl(t,St,e.memoizedState.cache),Xi();break;case 27:case 5:wt(t);break;case 4:Te(t,t.stateNode.containerInfo);break;case 10:Hl(t,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(i!==null)return i.dehydrated!==null?(Gl(t),t.flags|=128,null):(n&t.child.childLanes)!==0?xm(e,t,n):(Gl(t),e=Al(e,t,n),e!==null?e.sibling:null);Gl(t);break;case 19:var u=(e.flags&128)!==0;if(i=(n&t.childLanes)!==0,i||(Qi(e,t,n,!1),i=(n&t.childLanes)!==0),u){if(i)return wm(e,t,n);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),ee(xt,xt.current),i)break;return null;case 22:case 23:return t.lanes=0,ym(e,t,n);case 24:Hl(t,St,e.memoizedState.cache)}return Al(e,t,n)}function Am(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Tt=!0;else{if(!cc(e,n)&&(t.flags&128)===0)return Tt=!1,Ov(e,t,n);Tt=(e.flags&131072)!==0}else Tt=!1,He&&(t.flags&1048576)!==0&&eh(t,hs,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,u=i._init;if(i=u(i._payload),t.type=i,typeof i=="function")bo(i)?(e=wa(i,e),t.tag=1,t=bm(null,t,i,e,n)):(t.tag=0,t=lc(null,t,i,e,n));else{if(i!=null){if(u=i.$$typeof,u===X){t.tag=11,t=hm(null,t,i,e,n);break e}else if(u===W){t.tag=14,t=mm(null,t,i,e,n);break e}}throw t=de(i)||i,Error(s(306,t,""))}}return t;case 0:return lc(e,t,t.type,t.pendingProps,n);case 1:return i=t.type,u=wa(i,t.pendingProps),bm(e,t,i,u,n);case 3:e:{if(Te(t,t.stateNode.containerInfo),e===null)throw Error(s(387));i=t.pendingProps;var c=t.memoizedState;u=c.element,Mo(e,t),tr(t,i,null,n);var y=t.memoizedState;if(i=y.cache,Hl(t,St,i),i!==c.cache&&Ro(t,[St],n,!0),er(),i=y.element,c.isDehydrated)if(c={element:i,isDehydrated:!1,cache:y.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Sm(e,t,i,n);break e}else if(i!==u){u=bn(Error(s(424)),t),Zi(u),t=Sm(e,t,i,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(it=Mn(e.firstChild),qt=t,He=!0,ga=null,Zn=!0,n=nm(t,null,i,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Xi(),i===u){t=Al(e,t,n);break e}Ct(e,t,i,n)}t=t.child}return t;case 26:return Ms(e,t),e===null?(n=Op(t.type,null,t.pendingProps,null))?t.memoizedState=n:He||(n=t.type,e=t.pendingProps,i=Qs(xe.current).createElement(n),i[ue]=t,i[me]=e,Mt(i,n,e),ot(i),t.stateNode=i):t.memoizedState=Op(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return wt(t),e===null&&He&&(i=t.stateNode=_p(t.type,t.pendingProps,xe.current),qt=t,Zn=!0,u=it,ea(t.type)?(Gc=u,it=Mn(i.firstChild)):it=u),Ct(e,t,t.pendingProps.children,n),Ms(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&He&&((u=i=it)&&(i=nb(i,t.type,t.pendingProps,Zn),i!==null?(t.stateNode=i,qt=t,it=Mn(i.firstChild),Zn=!1,u=!0):u=!1),u||va(t)),wt(t),u=t.type,c=t.pendingProps,y=e!==null?e.memoizedProps:null,i=c.children,qc(u,c)?i=null:y!==null&&qc(u,y)&&(t.flags|=32),t.memoizedState!==null&&(u=Ho(e,t,Sv,null,null,n),wr._currentValue=u),Ms(e,t),Ct(e,t,i,n),t.child;case 6:return e===null&&He&&((e=n=it)&&(n=lb(n,t.pendingProps,Zn),n!==null?(t.stateNode=n,qt=t,it=null,e=!0):e=!1),e||va(t)),null;case 13:return xm(e,t,n);case 4:return Te(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=ai(t,null,i,n):Ct(e,t,i,n),t.child;case 11:return hm(e,t,t.type,t.pendingProps,n);case 7:return Ct(e,t,t.pendingProps,n),t.child;case 8:return Ct(e,t,t.pendingProps.children,n),t.child;case 12:return Ct(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,Hl(t,t.type,i.value),Ct(e,t,i.children,n),t.child;case 9:return u=t.type._context,i=t.pendingProps.children,Sa(t),u=zt(u),i=i(u),t.flags|=1,Ct(e,t,i,n),t.child;case 14:return mm(e,t,t.type,t.pendingProps,n);case 15:return pm(e,t,t.type,t.pendingProps,n);case 19:return wm(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},e===null?(n=Us(i,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=gl(e.child,i),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return ym(e,t,n);case 24:return Sa(t),i=zt(St),e===null?(u=Do(),u===null&&(u=Ie,c=Oo(),u.pooledCache=c,c.refCount++,c!==null&&(u.pooledCacheLanes|=n),u=c),t.memoizedState={parent:i,cache:u},No(t),Hl(t,St,u)):((e.lanes&n)!==0&&(Mo(e,t),tr(t,null,null,n),er()),u=e.memoizedState,c=t.memoizedState,u.parent!==i?(u={parent:i,cache:i},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),Hl(t,St,i)):(i=c.cache,Hl(t,St,i),i!==u.cache&&Ro(t,[St],n,!0))),Ct(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function _l(e){e.flags|=4}function _m(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Mp(t)){if(t=wn.current,t!==null&&((Be&4194048)===Be?Qn!==null:(Be&62914560)!==Be&&(Be&536870912)===0||t!==Qn))throw Wi=Co,uh;e.flags|=8192}}function zs(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?dl():536870912,e.lanes|=t,ui|=t)}function ur(e,t){if(!He)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function lt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,i=0;if(t)for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,i|=u.subtreeFlags&65011712,i|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)n|=u.lanes|u.childLanes,i|=u.subtreeFlags,i|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function jv(e,t,n){var i=t.pendingProps;switch(wo(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return lt(t),null;case 1:return lt(t),null;case 3:return n=t.stateNode,i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),xl(St),bt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&($i(t)?_l(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,lh())),lt(t),null;case 26:return n=t.memoizedState,e===null?(_l(t),n!==null?(lt(t),_m(t,n)):(lt(t),t.flags&=-16777217)):n?n!==e.memoizedState?(_l(t),lt(t),_m(t,n)):(lt(t),t.flags&=-16777217):(e.memoizedProps!==i&&_l(t),lt(t),t.flags&=-16777217),null;case 27:jt(t),n=xe.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==i&&_l(t);else{if(!i){if(t.stateNode===null)throw Error(s(166));return lt(t),null}e=he.current,$i(t)?th(t):(e=_p(u,i,n),t.stateNode=e,_l(t))}return lt(t),null;case 5:if(jt(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==i&&_l(t);else{if(!i){if(t.stateNode===null)throw Error(s(166));return lt(t),null}if(e=he.current,$i(t))th(t);else{switch(u=Qs(xe.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof i.is=="string"?u.createElement("select",{is:i.is}):u.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e=typeof i.is=="string"?u.createElement(n,{is:i.is}):u.createElement(n)}}e[ue]=t,e[me]=i;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(Mt(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&_l(t)}}return lt(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==i&&_l(t);else{if(typeof i!="string"&&t.stateNode===null)throw Error(s(166));if(e=xe.current,$i(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,u=qt,u!==null)switch(u.tag){case 27:case 5:i=u.memoizedProps}e[ue]=t,e=!!(e.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||vp(e.nodeValue,n)),e||va(t)}else e=Qs(e).createTextNode(i),e[ue]=t,t.stateNode=e}return lt(t),null;case 13:if(i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=$i(t),i!==null&&i.dehydrated!==null){if(e===null){if(!u)throw Error(s(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(s(317));u[ue]=t}else Xi(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;lt(t),u=!1}else u=lh(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(wl(t),t):(wl(t),null)}if(wl(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=i!==null,e=e!==null&&e.memoizedState!==null,n){i=t.child,u=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(u=i.alternate.memoizedState.cachePool.pool);var c=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(c=i.memoizedState.cachePool.pool),c!==u&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),zs(t,t.updateQueue),lt(t),null;case 4:return bt(),e===null&&zc(t.stateNode.containerInfo),lt(t),null;case 10:return xl(t.type),lt(t),null;case 19:if(ae(xt),u=t.memoizedState,u===null)return lt(t),null;if(i=(t.flags&128)!==0,c=u.rendering,c===null)if(i)ur(u,!1);else{if(rt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Ds(e),c!==null){for(t.flags|=128,ur(u,!1),e=c.updateQueue,t.updateQueue=e,zs(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Id(n,e),n=n.sibling;return ee(xt,xt.current&1|2),t.child}e=e.sibling}u.tail!==null&&st()>ks&&(t.flags|=128,i=!0,ur(u,!1),t.lanes=4194304)}else{if(!i)if(e=Ds(c),e!==null){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,zs(t,e),ur(u,!0),u.tail===null&&u.tailMode==="hidden"&&!c.alternate&&!He)return lt(t),null}else 2*st()-u.renderingStartTime>ks&&n!==536870912&&(t.flags|=128,i=!0,ur(u,!1),t.lanes=4194304);u.isBackwards?(c.sibling=t.child,t.child=c):(e=u.last,e!==null?e.sibling=c:t.child=c,u.last=c)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=st(),t.sibling=null,e=xt.current,ee(xt,i?e&1|2:e&1),t):(lt(t),null);case 22:case 23:return wl(t),Bo(),i=t.memoizedState!==null,e!==null?e.memoizedState!==null!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?(n&536870912)!==0&&(t.flags&128)===0&&(lt(t),t.subtreeFlags&6&&(t.flags|=8192)):lt(t),n=t.updateQueue,n!==null&&zs(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),i=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),e!==null&&ae(xa),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),xl(St),lt(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function Dv(e,t){switch(wo(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return xl(St),bt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return jt(t),null;case 13:if(wl(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Xi()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ae(xt),null;case 4:return bt(),null;case 10:return xl(t.type),null;case 22:case 23:return wl(t),Bo(),e!==null&&ae(xa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return xl(St),null;case 25:return null;default:return null}}function Tm(e,t){switch(wo(t),t.tag){case 3:xl(St),bt();break;case 26:case 27:case 5:jt(t);break;case 4:bt();break;case 13:wl(t);break;case 19:ae(xt);break;case 10:xl(t.type);break;case 22:case 23:wl(t),Bo(),e!==null&&ae(xa);break;case 24:xl(St)}}function or(e,t){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var u=i.next;n=u;do{if((n.tag&e)===e){i=void 0;var c=n.create,y=n.inst;i=c(),y.destroy=i}n=n.next}while(n!==u)}}catch(b){Ke(t,t.return,b)}}function Xl(e,t,n){try{var i=t.updateQueue,u=i!==null?i.lastEffect:null;if(u!==null){var c=u.next;i=c;do{if((i.tag&e)===e){var y=i.inst,b=y.destroy;if(b!==void 0){y.destroy=void 0,u=t;var A=n,L=b;try{L()}catch($){Ke(u,A,$)}}}i=i.next}while(i!==c)}}catch($){Ke(t,t.return,$)}}function Rm(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{mh(t,n)}catch(i){Ke(e,e.return,i)}}}function Om(e,t,n){n.props=wa(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(i){Ke(e,t,i)}}function cr(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;case 30:i=e.stateNode;break;default:i=e.stateNode}typeof n=="function"?e.refCleanup=n(i):n.current=i}}catch(u){Ke(e,t,u)}}function Kn(e,t){var n=e.ref,i=e.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(u){Ke(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(u){Ke(e,t,u)}else n.current=null}function jm(e){var t=e.type,n=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break e;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(u){Ke(e,e.return,u)}}function fc(e,t,n){try{var i=e.stateNode;Pv(i,e.type,n,t),i[me]=t}catch(u){Ke(e,e.return,u)}}function Dm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ea(e.type)||e.tag===4}function dc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Dm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ea(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hc(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Zs));else if(i!==4&&(i===27&&ea(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(hc(e,t,n),e=e.sibling;e!==null;)hc(e,t,n),e=e.sibling}function Ls(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(i!==4&&(i===27&&ea(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Ls(e,t,n),e=e.sibling;e!==null;)Ls(e,t,n),e=e.sibling}function Cm(e){var t=e.stateNode,n=e.memoizedProps;try{for(var i=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);Mt(t,i,n),t[ue]=e,t[me]=n}catch(c){Ke(e,e.return,c)}}var Tl=!1,ft=!1,mc=!1,Nm=typeof WeakSet=="function"?WeakSet:Set,Rt=null;function Cv(e,t){if(e=e.containerInfo,kc=eu,e=Yd(e),fo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var u=i.anchorOffset,c=i.focusNode;i=i.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var y=0,b=-1,A=-1,L=0,$=0,Q=e,k=null;t:for(;;){for(var q;Q!==n||u!==0&&Q.nodeType!==3||(b=y+u),Q!==c||i!==0&&Q.nodeType!==3||(A=y+i),Q.nodeType===3&&(y+=Q.nodeValue.length),(q=Q.firstChild)!==null;)k=Q,Q=q;for(;;){if(Q===e)break t;if(k===n&&++L===u&&(b=y),k===c&&++$===i&&(A=y),(q=Q.nextSibling)!==null)break;Q=k,k=Q.parentNode}Q=q}n=b===-1||A===-1?null:{start:b,end:A}}else n=null}n=n||{start:0,end:0}}else n=null;for(Hc={focusedElem:e,selectionRange:n},eu=!1,Rt=t;Rt!==null;)if(t=Rt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Rt=e;else for(;Rt!==null;){switch(t=Rt,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,u=c.memoizedProps,c=c.memoizedState,i=n.stateNode;try{var Ae=wa(n.type,u,n.elementType===n.type);e=i.getSnapshotBeforeUpdate(Ae,c),i.__reactInternalSnapshotBeforeUpdate=e}catch(Se){Ke(n,n.return,Se)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Fc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Fc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,Rt=e;break}Rt=t.return}}function Mm(e,t,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Zl(e,n),i&4&&or(5,n);break;case 1:if(Zl(e,n),i&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(y){Ke(n,n.return,y)}else{var u=wa(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(y){Ke(n,n.return,y)}}i&64&&Rm(n),i&512&&cr(n,n.return);break;case 3:if(Zl(e,n),i&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{mh(e,t)}catch(y){Ke(n,n.return,y)}}break;case 27:t===null&&i&4&&Cm(n);case 26:case 5:Zl(e,n),t===null&&i&4&&jm(n),i&512&&cr(n,n.return);break;case 12:Zl(e,n);break;case 13:Zl(e,n),i&4&&Lm(e,n),i&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=qv.bind(null,n),ab(e,n))));break;case 22:if(i=n.memoizedState!==null||Tl,!i){t=t!==null&&t.memoizedState!==null||ft,u=Tl;var c=ft;Tl=i,(ft=t)&&!c?Ql(e,n,(n.subtreeFlags&8772)!==0):Zl(e,n),Tl=u,ft=c}break;case 30:break;default:Zl(e,n)}}function Um(e){var t=e.alternate;t!==null&&(e.alternate=null,Um(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Ge(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var tt=null,Kt=!1;function Rl(e,t,n){for(n=n.child;n!==null;)zm(e,t,n),n=n.sibling}function zm(e,t,n){if(K&&typeof K.onCommitFiberUnmount=="function")try{K.onCommitFiberUnmount(P,n)}catch{}switch(n.tag){case 26:ft||Kn(n,t),Rl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:ft||Kn(n,t);var i=tt,u=Kt;ea(n.type)&&(tt=n.stateNode,Kt=!1),Rl(e,t,n),br(n.stateNode),tt=i,Kt=u;break;case 5:ft||Kn(n,t);case 6:if(i=tt,u=Kt,tt=null,Rl(e,t,n),tt=i,Kt=u,tt!==null)if(Kt)try{(tt.nodeType===9?tt.body:tt.nodeName==="HTML"?tt.ownerDocument.body:tt).removeChild(n.stateNode)}catch(c){Ke(n,t,c)}else try{tt.removeChild(n.stateNode)}catch(c){Ke(n,t,c)}break;case 18:tt!==null&&(Kt?(e=tt,wp(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Rr(e)):wp(tt,n.stateNode));break;case 4:i=tt,u=Kt,tt=n.stateNode.containerInfo,Kt=!0,Rl(e,t,n),tt=i,Kt=u;break;case 0:case 11:case 14:case 15:ft||Xl(2,n,t),ft||Xl(4,n,t),Rl(e,t,n);break;case 1:ft||(Kn(n,t),i=n.stateNode,typeof i.componentWillUnmount=="function"&&Om(n,t,i)),Rl(e,t,n);break;case 21:Rl(e,t,n);break;case 22:ft=(i=ft)||n.memoizedState!==null,Rl(e,t,n),ft=i;break;default:Rl(e,t,n)}}function Lm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Rr(e)}catch(n){Ke(t,t.return,n)}}function Nv(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Nm),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Nm),t;default:throw Error(s(435,e.tag))}}function pc(e,t){var n=Nv(e);t.forEach(function(i){var u=Vv.bind(null,e,i);n.has(i)||(n.add(i),i.then(u,u))})}function un(e,t){var n=t.deletions;if(n!==null)for(var i=0;i<n.length;i++){var u=n[i],c=e,y=t,b=y;e:for(;b!==null;){switch(b.tag){case 27:if(ea(b.type)){tt=b.stateNode,Kt=!1;break e}break;case 5:tt=b.stateNode,Kt=!1;break e;case 3:case 4:tt=b.stateNode.containerInfo,Kt=!0;break e}b=b.return}if(tt===null)throw Error(s(160));zm(c,y,u),tt=null,Kt=!1,c=u.alternate,c!==null&&(c.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Bm(t,e),t=t.sibling}var Nn=null;function Bm(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:un(t,e),on(e),i&4&&(Xl(3,e,e.return),or(3,e),Xl(5,e,e.return));break;case 1:un(t,e),on(e),i&512&&(ft||n===null||Kn(n,n.return)),i&64&&Tl&&(e=e.updateQueue,e!==null&&(i=e.callbacks,i!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var u=Nn;if(un(t,e),on(e),i&512&&(ft||n===null||Kn(n,n.return)),i&4){var c=n!==null?n.memoizedState:null;if(i=e.memoizedState,n===null)if(i===null)if(e.stateNode===null){e:{i=e.type,n=e.memoizedProps,u=u.ownerDocument||u;t:switch(i){case"title":c=u.getElementsByTagName("title")[0],(!c||c[_t]||c[ue]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=u.createElement(i),u.head.insertBefore(c,u.querySelector("head > title"))),Mt(c,i,n),c[ue]=e,ot(c),i=c;break e;case"link":var y=Cp("link","href",u).get(i+(n.href||""));if(y){for(var b=0;b<y.length;b++)if(c=y[b],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){y.splice(b,1);break t}}c=u.createElement(i),Mt(c,i,n),u.head.appendChild(c);break;case"meta":if(y=Cp("meta","content",u).get(i+(n.content||""))){for(b=0;b<y.length;b++)if(c=y[b],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){y.splice(b,1);break t}}c=u.createElement(i),Mt(c,i,n),u.head.appendChild(c);break;default:throw Error(s(468,i))}c[ue]=e,ot(c),i=c}e.stateNode=i}else Np(u,e.type,e.stateNode);else e.stateNode=Dp(u,i,e.memoizedProps);else c!==i?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,i===null?Np(u,e.type,e.stateNode):Dp(u,i,e.memoizedProps)):i===null&&e.stateNode!==null&&fc(e,e.memoizedProps,n.memoizedProps)}break;case 27:un(t,e),on(e),i&512&&(ft||n===null||Kn(n,n.return)),n!==null&&i&4&&fc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(un(t,e),on(e),i&512&&(ft||n===null||Kn(n,n.return)),e.flags&32){u=e.stateNode;try{qa(u,"")}catch(q){Ke(e,e.return,q)}}i&4&&e.stateNode!=null&&(u=e.memoizedProps,fc(e,u,n!==null?n.memoizedProps:u)),i&1024&&(mc=!0);break;case 6:if(un(t,e),on(e),i&4){if(e.stateNode===null)throw Error(s(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(q){Ke(e,e.return,q)}}break;case 3:if(Ps=null,u=Nn,Nn=Ks(t.containerInfo),un(t,e),Nn=u,on(e),i&4&&n!==null&&n.memoizedState.isDehydrated)try{Rr(t.containerInfo)}catch(q){Ke(e,e.return,q)}mc&&(mc=!1,km(e));break;case 4:i=Nn,Nn=Ks(e.stateNode.containerInfo),un(t,e),on(e),Nn=i;break;case 12:un(t,e),on(e);break;case 13:un(t,e),on(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(xc=st()),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,pc(e,i)));break;case 22:u=e.memoizedState!==null;var A=n!==null&&n.memoizedState!==null,L=Tl,$=ft;if(Tl=L||u,ft=$||A,un(t,e),ft=$,Tl=L,on(e),i&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(n===null||A||Tl||ft||Aa(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){A=n=t;try{if(c=A.stateNode,u)y=c.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{b=A.stateNode;var Q=A.memoizedProps.style,k=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;b.style.display=k==null||typeof k=="boolean"?"":(""+k).trim()}}catch(q){Ke(A,A.return,q)}}}else if(t.tag===6){if(n===null){A=t;try{A.stateNode.nodeValue=u?"":A.memoizedProps}catch(q){Ke(A,A.return,q)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}i&4&&(i=e.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,pc(e,n))));break;case 19:un(t,e),on(e),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,pc(e,i)));break;case 30:break;case 21:break;default:un(t,e),on(e)}}function on(e){var t=e.flags;if(t&2){try{for(var n,i=e.return;i!==null;){if(Dm(i)){n=i;break}i=i.return}if(n==null)throw Error(s(160));switch(n.tag){case 27:var u=n.stateNode,c=dc(e);Ls(e,c,u);break;case 5:var y=n.stateNode;n.flags&32&&(qa(y,""),n.flags&=-33);var b=dc(e);Ls(e,b,y);break;case 3:case 4:var A=n.stateNode.containerInfo,L=dc(e);hc(e,L,A);break;default:throw Error(s(161))}}catch($){Ke(e,e.return,$)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function km(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;km(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Zl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Mm(e,t.alternate,t),t=t.sibling}function Aa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Xl(4,t,t.return),Aa(t);break;case 1:Kn(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Om(t,t.return,n),Aa(t);break;case 27:br(t.stateNode);case 26:case 5:Kn(t,t.return),Aa(t);break;case 22:t.memoizedState===null&&Aa(t);break;case 30:Aa(t);break;default:Aa(t)}e=e.sibling}}function Ql(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var i=t.alternate,u=e,c=t,y=c.flags;switch(c.tag){case 0:case 11:case 15:Ql(u,c,n),or(4,c);break;case 1:if(Ql(u,c,n),i=c,u=i.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(L){Ke(i,i.return,L)}if(i=c,u=i.updateQueue,u!==null){var b=i.stateNode;try{var A=u.shared.hiddenCallbacks;if(A!==null)for(u.shared.hiddenCallbacks=null,u=0;u<A.length;u++)hh(A[u],b)}catch(L){Ke(i,i.return,L)}}n&&y&64&&Rm(c),cr(c,c.return);break;case 27:Cm(c);case 26:case 5:Ql(u,c,n),n&&i===null&&y&4&&jm(c),cr(c,c.return);break;case 12:Ql(u,c,n);break;case 13:Ql(u,c,n),n&&y&4&&Lm(u,c);break;case 22:c.memoizedState===null&&Ql(u,c,n),cr(c,c.return);break;case 30:break;default:Ql(u,c,n)}t=t.sibling}}function yc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Ki(n))}function gc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ki(e))}function Jn(e,t,n,i){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Hm(e,t,n,i),t=t.sibling}function Hm(e,t,n,i){var u=t.flags;switch(t.tag){case 0:case 11:case 15:Jn(e,t,n,i),u&2048&&or(9,t);break;case 1:Jn(e,t,n,i);break;case 3:Jn(e,t,n,i),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ki(e)));break;case 12:if(u&2048){Jn(e,t,n,i),e=t.stateNode;try{var c=t.memoizedProps,y=c.id,b=c.onPostCommit;typeof b=="function"&&b(y,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(A){Ke(t,t.return,A)}}else Jn(e,t,n,i);break;case 13:Jn(e,t,n,i);break;case 23:break;case 22:c=t.stateNode,y=t.alternate,t.memoizedState!==null?c._visibility&2?Jn(e,t,n,i):fr(e,t):c._visibility&2?Jn(e,t,n,i):(c._visibility|=2,ii(e,t,n,i,(t.subtreeFlags&10256)!==0)),u&2048&&yc(y,t);break;case 24:Jn(e,t,n,i),u&2048&&gc(t.alternate,t);break;default:Jn(e,t,n,i)}}function ii(e,t,n,i,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,y=t,b=n,A=i,L=y.flags;switch(y.tag){case 0:case 11:case 15:ii(c,y,b,A,u),or(8,y);break;case 23:break;case 22:var $=y.stateNode;y.memoizedState!==null?$._visibility&2?ii(c,y,b,A,u):fr(c,y):($._visibility|=2,ii(c,y,b,A,u)),u&&L&2048&&yc(y.alternate,y);break;case 24:ii(c,y,b,A,u),u&&L&2048&&gc(y.alternate,y);break;default:ii(c,y,b,A,u)}t=t.sibling}}function fr(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,i=t,u=i.flags;switch(i.tag){case 22:fr(n,i),u&2048&&yc(i.alternate,i);break;case 24:fr(n,i),u&2048&&gc(i.alternate,i);break;default:fr(n,i)}t=t.sibling}}var dr=8192;function ri(e){if(e.subtreeFlags&dr)for(e=e.child;e!==null;)qm(e),e=e.sibling}function qm(e){switch(e.tag){case 26:ri(e),e.flags&dr&&e.memoizedState!==null&&gb(Nn,e.memoizedState,e.memoizedProps);break;case 5:ri(e);break;case 3:case 4:var t=Nn;Nn=Ks(e.stateNode.containerInfo),ri(e),Nn=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=dr,dr=16777216,ri(e),dr=t):ri(e));break;default:ri(e)}}function Vm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function hr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];Rt=i,Ym(i,e)}Vm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Fm(e),e=e.sibling}function Fm(e){switch(e.tag){case 0:case 11:case 15:hr(e),e.flags&2048&&Xl(9,e,e.return);break;case 3:hr(e);break;case 12:hr(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Bs(e)):hr(e);break;default:hr(e)}}function Bs(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];Rt=i,Ym(i,e)}Vm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Xl(8,t,t.return),Bs(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Bs(t));break;default:Bs(t)}e=e.sibling}}function Ym(e,t){for(;Rt!==null;){var n=Rt;switch(n.tag){case 0:case 11:case 15:Xl(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Ki(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,Rt=i;else e:for(n=e;Rt!==null;){i=Rt;var u=i.sibling,c=i.return;if(Um(i),i===n){Rt=null;break e}if(u!==null){u.return=c,Rt=u;break e}Rt=c}}}var Mv={getCacheForType:function(e){var t=zt(St),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Uv=typeof WeakMap=="function"?WeakMap:Map,Ve=0,Ie=null,Ue=null,Be=0,Fe=0,cn=null,Kl=!1,si=!1,vc=!1,Ol=0,rt=0,Jl=0,_a=0,bc=0,An=0,ui=0,mr=null,Jt=null,Sc=!1,xc=0,ks=1/0,Hs=null,Pl=null,Nt=0,Wl=null,oi=null,ci=0,Ec=0,wc=null,Gm=null,pr=0,Ac=null;function fn(){if((Ve&2)!==0&&Be!==0)return Be&-Be;if(H.T!==null){var e=Pa;return e!==0?e:Cc()}return Ut()}function $m(){An===0&&(An=(Be&536870912)===0||He?fl():536870912);var e=wn.current;return e!==null&&(e.flags|=32),An}function dn(e,t,n){(e===Ie&&(Fe===2||Fe===9)||e.cancelPendingCommit!==null)&&(fi(e,0),Il(e,Be,An,!1)),jn(e,n),((Ve&2)===0||e!==Ie)&&(e===Ie&&((Ve&2)===0&&(_a|=n),rt===4&&Il(e,Be,An,!1)),Pn(e))}function Xm(e,t,n){if((Ve&6)!==0)throw Error(s(327));var i=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Xt(e,t),u=i?Bv(e,t):Rc(e,t,!0),c=i;do{if(u===0){si&&!i&&Il(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!zv(n)){u=Rc(e,t,!1),c=!1;continue}if(u===2){if(c=t,e.errorRecoveryDisabledLanes&c)var y=0;else y=e.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){t=y;e:{var b=e;u=mr;var A=b.current.memoizedState.isDehydrated;if(A&&(fi(b,y).flags|=256),y=Rc(b,y,!1),y!==2){if(vc&&!A){b.errorRecoveryDisabledLanes|=c,_a|=c,u=4;break e}c=Jt,Jt=u,c!==null&&(Jt===null?Jt=c:Jt.push.apply(Jt,c))}u=y}if(c=!1,u!==2)continue}}if(u===1){fi(e,0),Il(e,t,0,!0);break}e:{switch(i=e,c=u,c){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:Il(i,t,An,!Kl);break e;case 2:Jt=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(u=xc+300-st(),10<u)){if(Il(i,t,An,!Kl),ln(i,0,!0)!==0)break e;i.timeoutHandle=xp(Zm.bind(null,i,n,Jt,Hs,Sc,t,An,_a,ui,Kl,c,2,-0,0),u);break e}Zm(i,n,Jt,Hs,Sc,t,An,_a,ui,Kl,c,0,-0,0)}}break}while(!0);Pn(e)}function Zm(e,t,n,i,u,c,y,b,A,L,$,Q,k,q){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(Er={stylesheets:null,count:0,unsuspend:yb},qm(t),Q=vb(),Q!==null)){e.cancelPendingCommit=Q(ep.bind(null,e,t,c,n,i,u,y,b,A,$,1,k,q)),Il(e,c,y,!L);return}ep(e,t,c,n,i,u,y,b,A)}function zv(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var u=n[i],c=u.getSnapshot;u=u.value;try{if(!rn(c(),u))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Il(e,t,n,i){t&=~bc,t&=~_a,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var u=t;0<u;){var c=31-ve(u),y=1<<c;i[c]=-1,u&=~y}n!==0&&ut(e,n,t)}function qs(){return(Ve&6)===0?(yr(0),!1):!0}function _c(){if(Ue!==null){if(Fe===0)var e=Ue.return;else e=Ue,Sl=ba=null,Fo(e),li=null,rr=0,e=Ue;for(;e!==null;)Tm(e.alternate,e),e=e.return;Ue=null}}function fi(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,Iv(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),_c(),Ie=e,Ue=n=gl(e.current,null),Be=t,Fe=0,cn=null,Kl=!1,si=Xt(e,t),vc=!1,ui=An=bc=_a=Jl=rt=0,Jt=mr=null,Sc=!1,(t&8)!==0&&(t|=t&32);var i=e.entangledLanes;if(i!==0)for(e=e.entanglements,i&=t;0<i;){var u=31-ve(i),c=1<<u;t|=e[u],i&=~c}return Ol=t,us(),n}function Qm(e,t){Ce=null,H.H=Rs,t===Pi||t===gs?(t=fh(),Fe=3):t===uh?(t=fh(),Fe=4):Fe=t===dm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,cn=t,Ue===null&&(rt=1,Ns(e,bn(t,e.current)))}function Km(){var e=H.H;return H.H=Rs,e===null?Rs:e}function Jm(){var e=H.A;return H.A=Mv,e}function Tc(){rt=4,Kl||(Be&4194048)!==Be&&wn.current!==null||(si=!0),(Jl&134217727)===0&&(_a&134217727)===0||Ie===null||Il(Ie,Be,An,!1)}function Rc(e,t,n){var i=Ve;Ve|=2;var u=Km(),c=Jm();(Ie!==e||Be!==t)&&(Hs=null,fi(e,t)),t=!1;var y=rt;e:do try{if(Fe!==0&&Ue!==null){var b=Ue,A=cn;switch(Fe){case 8:_c(),y=6;break e;case 3:case 2:case 9:case 6:wn.current===null&&(t=!0);var L=Fe;if(Fe=0,cn=null,di(e,b,A,L),n&&si){y=0;break e}break;default:L=Fe,Fe=0,cn=null,di(e,b,A,L)}}Lv(),y=rt;break}catch($){Qm(e,$)}while(!0);return t&&e.shellSuspendCounter++,Sl=ba=null,Ve=i,H.H=u,H.A=c,Ue===null&&(Ie=null,Be=0,us()),y}function Lv(){for(;Ue!==null;)Pm(Ue)}function Bv(e,t){var n=Ve;Ve|=2;var i=Km(),u=Jm();Ie!==e||Be!==t?(Hs=null,ks=st()+500,fi(e,t)):si=Xt(e,t);e:do try{if(Fe!==0&&Ue!==null){t=Ue;var c=cn;t:switch(Fe){case 1:Fe=0,cn=null,di(e,t,c,1);break;case 2:case 9:if(oh(c)){Fe=0,cn=null,Wm(t);break}t=function(){Fe!==2&&Fe!==9||Ie!==e||(Fe=7),Pn(e)},c.then(t,t);break e;case 3:Fe=7;break e;case 4:Fe=5;break e;case 7:oh(c)?(Fe=0,cn=null,Wm(t)):(Fe=0,cn=null,di(e,t,c,7));break;case 5:var y=null;switch(Ue.tag){case 26:y=Ue.memoizedState;case 5:case 27:var b=Ue;if(!y||Mp(y)){Fe=0,cn=null;var A=b.sibling;if(A!==null)Ue=A;else{var L=b.return;L!==null?(Ue=L,Vs(L)):Ue=null}break t}}Fe=0,cn=null,di(e,t,c,5);break;case 6:Fe=0,cn=null,di(e,t,c,6);break;case 8:_c(),rt=6;break e;default:throw Error(s(462))}}kv();break}catch($){Qm(e,$)}while(!0);return Sl=ba=null,H.H=i,H.A=u,Ve=n,Ue!==null?0:(Ie=null,Be=0,us(),rt)}function kv(){for(;Ue!==null&&!Dt();)Pm(Ue)}function Pm(e){var t=Am(e.alternate,e,Ol);e.memoizedProps=e.pendingProps,t===null?Vs(e):Ue=t}function Wm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=vm(n,t,t.pendingProps,t.type,void 0,Be);break;case 11:t=vm(n,t,t.pendingProps,t.type.render,t.ref,Be);break;case 5:Fo(t);default:Tm(n,t),t=Ue=Id(t,Ol),t=Am(n,t,Ol)}e.memoizedProps=e.pendingProps,t===null?Vs(e):Ue=t}function di(e,t,n,i){Sl=ba=null,Fo(t),li=null,rr=0;var u=t.return;try{if(Rv(e,u,t,n,Be)){rt=1,Ns(e,bn(n,e.current)),Ue=null;return}}catch(c){if(u!==null)throw Ue=u,c;rt=1,Ns(e,bn(n,e.current)),Ue=null;return}t.flags&32768?(He||i===1?e=!0:si||(Be&536870912)!==0?e=!1:(Kl=e=!0,(i===2||i===9||i===3||i===6)&&(i=wn.current,i!==null&&i.tag===13&&(i.flags|=16384))),Im(t,e)):Vs(t)}function Vs(e){var t=e;do{if((t.flags&32768)!==0){Im(t,Kl);return}e=t.return;var n=jv(t.alternate,t,Ol);if(n!==null){Ue=n;return}if(t=t.sibling,t!==null){Ue=t;return}Ue=t=e}while(t!==null);rt===0&&(rt=5)}function Im(e,t){do{var n=Dv(e.alternate,e);if(n!==null){n.flags&=32767,Ue=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){Ue=e;return}Ue=e=n}while(e!==null);rt=6,Ue=null}function ep(e,t,n,i,u,c,y,b,A){e.cancelPendingCommit=null;do Fs();while(Nt!==0);if((Ve&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(c=t.lanes|t.childLanes,c|=go,La(e,n,c,y,b,A),e===Ie&&(Ue=Ie=null,Be=0),oi=t,Wl=e,ci=n,Ec=c,wc=u,Gm=i,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Fv(nn,function(){return ip(),null})):(e.callbackNode=null,e.callbackPriority=0),i=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||i){i=H.T,H.T=null,u=J.p,J.p=2,y=Ve,Ve|=4;try{Cv(e,t,n)}finally{Ve=y,J.p=u,H.T=i}}Nt=1,tp(),np(),lp()}}function tp(){if(Nt===1){Nt=0;var e=Wl,t=oi,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=H.T,H.T=null;var i=J.p;J.p=2;var u=Ve;Ve|=4;try{Bm(t,e);var c=Hc,y=Yd(e.containerInfo),b=c.focusedElem,A=c.selectionRange;if(y!==b&&b&&b.ownerDocument&&Fd(b.ownerDocument.documentElement,b)){if(A!==null&&fo(b)){var L=A.start,$=A.end;if($===void 0&&($=L),"selectionStart"in b)b.selectionStart=L,b.selectionEnd=Math.min($,b.value.length);else{var Q=b.ownerDocument||document,k=Q&&Q.defaultView||window;if(k.getSelection){var q=k.getSelection(),Ae=b.textContent.length,Se=Math.min(A.start,Ae),Ze=A.end===void 0?Se:Math.min(A.end,Ae);!q.extend&&Se>Ze&&(y=Ze,Ze=Se,Se=y);var U=Vd(b,Se),N=Vd(b,Ze);if(U&&N&&(q.rangeCount!==1||q.anchorNode!==U.node||q.anchorOffset!==U.offset||q.focusNode!==N.node||q.focusOffset!==N.offset)){var z=Q.createRange();z.setStart(U.node,U.offset),q.removeAllRanges(),Se>Ze?(q.addRange(z),q.extend(N.node,N.offset)):(z.setEnd(N.node,N.offset),q.addRange(z))}}}}for(Q=[],q=b;q=q.parentNode;)q.nodeType===1&&Q.push({element:q,left:q.scrollLeft,top:q.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<Q.length;b++){var Z=Q[b];Z.element.scrollLeft=Z.left,Z.element.scrollTop=Z.top}}eu=!!kc,Hc=kc=null}finally{Ve=u,J.p=i,H.T=n}}e.current=t,Nt=2}}function np(){if(Nt===2){Nt=0;var e=Wl,t=oi,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=H.T,H.T=null;var i=J.p;J.p=2;var u=Ve;Ve|=4;try{Mm(e,t.alternate,t)}finally{Ve=u,J.p=i,H.T=n}}Nt=3}}function lp(){if(Nt===4||Nt===3){Nt=0,ol();var e=Wl,t=oi,n=ci,i=Gm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Nt=5:(Nt=0,oi=Wl=null,ap(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(Pl=null),Ne(n),t=t.stateNode,K&&typeof K.onCommitFiberRoot=="function")try{K.onCommitFiberRoot(P,t,void 0,(t.current.flags&128)===128)}catch{}if(i!==null){t=H.T,u=J.p,J.p=2,H.T=null;try{for(var c=e.onRecoverableError,y=0;y<i.length;y++){var b=i[y];c(b.value,{componentStack:b.stack})}}finally{H.T=t,J.p=u}}(ci&3)!==0&&Fs(),Pn(e),u=e.pendingLanes,(n&4194090)!==0&&(u&42)!==0?e===Ac?pr++:(pr=0,Ac=e):pr=0,yr(0)}}function ap(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ki(t)))}function Fs(e){return tp(),np(),lp(),ip()}function ip(){if(Nt!==5)return!1;var e=Wl,t=Ec;Ec=0;var n=Ne(ci),i=H.T,u=J.p;try{J.p=32>n?32:n,H.T=null,n=wc,wc=null;var c=Wl,y=ci;if(Nt=0,oi=Wl=null,ci=0,(Ve&6)!==0)throw Error(s(331));var b=Ve;if(Ve|=4,Fm(c.current),Hm(c,c.current,y,n),Ve=b,yr(0,!1),K&&typeof K.onPostCommitFiberRoot=="function")try{K.onPostCommitFiberRoot(P,c)}catch{}return!0}finally{J.p=u,H.T=i,ap(e,t)}}function rp(e,t,n){t=bn(n,t),t=nc(e.stateNode,t,2),e=Fl(e,t,2),e!==null&&(jn(e,2),Pn(e))}function Ke(e,t,n){if(e.tag===3)rp(e,e,n);else for(;t!==null;){if(t.tag===3){rp(t,e,n);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Pl===null||!Pl.has(i))){e=bn(n,e),n=cm(2),i=Fl(t,n,2),i!==null&&(fm(n,i,t,e),jn(i,2),Pn(i));break}}t=t.return}}function Oc(e,t,n){var i=e.pingCache;if(i===null){i=e.pingCache=new Uv;var u=new Set;i.set(t,u)}else u=i.get(t),u===void 0&&(u=new Set,i.set(t,u));u.has(n)||(vc=!0,u.add(n),e=Hv.bind(null,e,t,n),t.then(e,e))}function Hv(e,t,n){var i=e.pingCache;i!==null&&i.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Ie===e&&(Be&n)===n&&(rt===4||rt===3&&(Be&62914560)===Be&&300>st()-xc?(Ve&2)===0&&fi(e,0):bc|=n,ui===Be&&(ui=0)),Pn(e)}function sp(e,t){t===0&&(t=dl()),e=Za(e,t),e!==null&&(jn(e,t),Pn(e))}function qv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),sp(e,n)}function Vv(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,u=e.memoizedState;u!==null&&(n=u.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(s(314))}i!==null&&i.delete(t),sp(e,n)}function Fv(e,t){return Gt(e,t)}var Ys=null,hi=null,jc=!1,Gs=!1,Dc=!1,Ta=0;function Pn(e){e!==hi&&e.next===null&&(hi===null?Ys=hi=e:hi=hi.next=e),Gs=!0,jc||(jc=!0,Gv())}function yr(e,t){if(!Dc&&Gs){Dc=!0;do for(var n=!1,i=Ys;i!==null;){if(e!==0){var u=i.pendingLanes;if(u===0)var c=0;else{var y=i.suspendedLanes,b=i.pingedLanes;c=(1<<31-ve(42|e)+1)-1,c&=u&~(y&~b),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,fp(i,c))}else c=Be,c=ln(i,i===Ie?c:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(c&3)===0||Xt(i,c)||(n=!0,fp(i,c));i=i.next}while(n);Dc=!1}}function Yv(){up()}function up(){Gs=jc=!1;var e=0;Ta!==0&&(Wv()&&(e=Ta),Ta=0);for(var t=st(),n=null,i=Ys;i!==null;){var u=i.next,c=op(i,t);c===0?(i.next=null,n===null?Ys=u:n.next=u,u===null&&(hi=n)):(n=i,(e!==0||(c&3)!==0)&&(Gs=!0)),i=u}yr(e)}function op(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,u=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var y=31-ve(c),b=1<<y,A=u[y];A===-1?((b&n)===0||(b&i)!==0)&&(u[y]=Gn(b,t)):A<=t&&(e.expiredLanes|=b),c&=~b}if(t=Ie,n=Be,n=ln(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i=e.callbackNode,n===0||e===t&&(Fe===2||Fe===9)||e.cancelPendingCommit!==null)return i!==null&&i!==null&&Yn(i),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Xt(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(i!==null&&Yn(i),Ne(n)){case 2:case 8:n=cl;break;case 32:n=nn;break;case 268435456:n=B;break;default:n=nn}return i=cp.bind(null,e),n=Gt(n,i),e.callbackPriority=t,e.callbackNode=n,t}return i!==null&&i!==null&&Yn(i),e.callbackPriority=2,e.callbackNode=null,2}function cp(e,t){if(Nt!==0&&Nt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Fs()&&e.callbackNode!==n)return null;var i=Be;return i=ln(e,e===Ie?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i===0?null:(Xm(e,i,t),op(e,st()),e.callbackNode!=null&&e.callbackNode===n?cp.bind(null,e):null)}function fp(e,t){if(Fs())return null;Xm(e,t,!0)}function Gv(){eb(function(){(Ve&6)!==0?Gt(Ye,Yv):up()})}function Cc(){return Ta===0&&(Ta=fl()),Ta}function dp(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ts(""+e)}function hp(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function $v(e,t,n,i,u){if(t==="submit"&&n&&n.stateNode===u){var c=dp((u[me]||null).action),y=i.submitter;y&&(t=(t=y[me]||null)?dp(t.formAction):y.getAttribute("formAction"),t!==null&&(c=t,y=null));var b=new is("action","action",null,i,u);e.push({event:b,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(Ta!==0){var A=y?hp(u,y):new FormData(u);Po(n,{pending:!0,data:A,method:u.method,action:c},null,A)}}else typeof c=="function"&&(b.preventDefault(),A=y?hp(u,y):new FormData(u),Po(n,{pending:!0,data:A,method:u.method,action:c},c,A))},currentTarget:u}]})}}for(var Nc=0;Nc<yo.length;Nc++){var Mc=yo[Nc],Xv=Mc.toLowerCase(),Zv=Mc[0].toUpperCase()+Mc.slice(1);Cn(Xv,"on"+Zv)}Cn(Xd,"onAnimationEnd"),Cn(Zd,"onAnimationIteration"),Cn(Qd,"onAnimationStart"),Cn("dblclick","onDoubleClick"),Cn("focusin","onFocus"),Cn("focusout","onBlur"),Cn(cv,"onTransitionRun"),Cn(fv,"onTransitionStart"),Cn(dv,"onTransitionCancel"),Cn(Kd,"onTransitionEnd"),Le("onMouseEnter",["mouseout","mouseover"]),Le("onMouseLeave",["mouseout","mouseover"]),Le("onPointerEnter",["pointerout","pointerover"]),Le("onPointerLeave",["pointerout","pointerover"]),Xn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Xn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Xn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Xn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Xn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Xn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var gr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Qv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(gr));function mp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var i=e[n],u=i.event;i=i.listeners;e:{var c=void 0;if(t)for(var y=i.length-1;0<=y;y--){var b=i[y],A=b.instance,L=b.currentTarget;if(b=b.listener,A!==c&&u.isPropagationStopped())break e;c=b,u.currentTarget=L;try{c(u)}catch($){Cs($)}u.currentTarget=null,c=A}else for(y=0;y<i.length;y++){if(b=i[y],A=b.instance,L=b.currentTarget,b=b.listener,A!==c&&u.isPropagationStopped())break e;c=b,u.currentTarget=L;try{c(u)}catch($){Cs($)}u.currentTarget=null,c=A}}}}function ze(e,t){var n=t[pt];n===void 0&&(n=t[pt]=new Set);var i=e+"__bubble";n.has(i)||(pp(t,e,2,!1),n.add(i))}function Uc(e,t,n){var i=0;t&&(i|=4),pp(n,e,i,t)}var $s="_reactListening"+Math.random().toString(36).slice(2);function zc(e){if(!e[$s]){e[$s]=!0,Ui.forEach(function(n){n!=="selectionchange"&&(Qv.has(n)||Uc(n,!1,e),Uc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[$s]||(t[$s]=!0,Uc("selectionchange",!1,t))}}function pp(e,t,n,i){switch(Hp(t)){case 2:var u=xb;break;case 8:u=Eb;break;default:u=Kc}n=u.bind(null,t,n,e),u=void 0,!no||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),i?u!==void 0?e.addEventListener(t,n,{capture:!0,passive:u}):e.addEventListener(t,n,!0):u!==void 0?e.addEventListener(t,n,{passive:u}):e.addEventListener(t,n,!1)}function Lc(e,t,n,i,u){var c=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var y=i.tag;if(y===3||y===4){var b=i.stateNode.containerInfo;if(b===u)break;if(y===4)for(y=i.return;y!==null;){var A=y.tag;if((A===3||A===4)&&y.stateNode.containerInfo===u)return;y=y.return}for(;b!==null;){if(y=an(b),y===null)return;if(A=y.tag,A===5||A===6||A===26||A===27){i=c=y;continue e}b=b.parentNode}}i=i.return}Ed(function(){var L=c,$=eo(n),Q=[];e:{var k=Jd.get(e);if(k!==void 0){var q=is,Ae=e;switch(e){case"keypress":if(ls(n)===0)break e;case"keydown":case"keyup":q=Fg;break;case"focusin":Ae="focus",q=ro;break;case"focusout":Ae="blur",q=ro;break;case"beforeblur":case"afterblur":q=ro;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":q=_d;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":q=Dg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":q=$g;break;case Xd:case Zd:case Qd:q=Mg;break;case Kd:q=Zg;break;case"scroll":case"scrollend":q=Og;break;case"wheel":q=Kg;break;case"copy":case"cut":case"paste":q=zg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":q=Rd;break;case"toggle":case"beforetoggle":q=Pg}var Se=(t&4)!==0,Ze=!Se&&(e==="scroll"||e==="scrollend"),U=Se?k!==null?k+"Capture":null:k;Se=[];for(var N=L,z;N!==null;){var Z=N;if(z=Z.stateNode,Z=Z.tag,Z!==5&&Z!==26&&Z!==27||z===null||U===null||(Z=Li(N,U),Z!=null&&Se.push(vr(N,Z,z))),Ze)break;N=N.return}0<Se.length&&(k=new q(k,Ae,null,n,$),Q.push({event:k,listeners:Se}))}}if((t&7)===0){e:{if(k=e==="mouseover"||e==="pointerover",q=e==="mouseout"||e==="pointerout",k&&n!==Iu&&(Ae=n.relatedTarget||n.fromElement)&&(an(Ae)||Ae[We]))break e;if((q||k)&&(k=$.window===$?$:(k=$.ownerDocument)?k.defaultView||k.parentWindow:window,q?(Ae=n.relatedTarget||n.toElement,q=L,Ae=Ae?an(Ae):null,Ae!==null&&(Ze=f(Ae),Se=Ae.tag,Ae!==Ze||Se!==5&&Se!==27&&Se!==6)&&(Ae=null)):(q=null,Ae=L),q!==Ae)){if(Se=_d,Z="onMouseLeave",U="onMouseEnter",N="mouse",(e==="pointerout"||e==="pointerover")&&(Se=Rd,Z="onPointerLeave",U="onPointerEnter",N="pointer"),Ze=q==null?k:ca(q),z=Ae==null?k:ca(Ae),k=new Se(Z,N+"leave",q,n,$),k.target=Ze,k.relatedTarget=z,Z=null,an($)===L&&(Se=new Se(U,N+"enter",Ae,n,$),Se.target=z,Se.relatedTarget=Ze,Z=Se),Ze=Z,q&&Ae)t:{for(Se=q,U=Ae,N=0,z=Se;z;z=mi(z))N++;for(z=0,Z=U;Z;Z=mi(Z))z++;for(;0<N-z;)Se=mi(Se),N--;for(;0<z-N;)U=mi(U),z--;for(;N--;){if(Se===U||U!==null&&Se===U.alternate)break t;Se=mi(Se),U=mi(U)}Se=null}else Se=null;q!==null&&yp(Q,k,q,Se,!1),Ae!==null&&Ze!==null&&yp(Q,Ze,Ae,Se,!0)}}e:{if(k=L?ca(L):window,q=k.nodeName&&k.nodeName.toLowerCase(),q==="select"||q==="input"&&k.type==="file")var oe=zd;else if(Md(k))if(Ld)oe=sv;else{oe=iv;var Me=av}else q=k.nodeName,!q||q.toLowerCase()!=="input"||k.type!=="checkbox"&&k.type!=="radio"?L&&Wu(L.elementType)&&(oe=zd):oe=rv;if(oe&&(oe=oe(e,L))){Ud(Q,oe,n,$);break e}Me&&Me(e,k,L),e==="focusout"&&L&&k.type==="number"&&L.memoizedProps.value!=null&&Pu(k,"number",k.value)}switch(Me=L?ca(L):window,e){case"focusin":(Md(Me)||Me.contentEditable==="true")&&(Ga=Me,ho=L,Gi=null);break;case"focusout":Gi=ho=Ga=null;break;case"mousedown":mo=!0;break;case"contextmenu":case"mouseup":case"dragend":mo=!1,Gd(Q,n,$);break;case"selectionchange":if(ov)break;case"keydown":case"keyup":Gd(Q,n,$)}var pe;if(uo)e:{switch(e){case"compositionstart":var Ee="onCompositionStart";break e;case"compositionend":Ee="onCompositionEnd";break e;case"compositionupdate":Ee="onCompositionUpdate";break e}Ee=void 0}else Ya?Cd(e,n)&&(Ee="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(Ee="onCompositionStart");Ee&&(Od&&n.locale!=="ko"&&(Ya||Ee!=="onCompositionStart"?Ee==="onCompositionEnd"&&Ya&&(pe=wd()):(kl=$,lo="value"in kl?kl.value:kl.textContent,Ya=!0)),Me=Xs(L,Ee),0<Me.length&&(Ee=new Td(Ee,e,null,n,$),Q.push({event:Ee,listeners:Me}),pe?Ee.data=pe:(pe=Nd(n),pe!==null&&(Ee.data=pe)))),(pe=Ig?ev(e,n):tv(e,n))&&(Ee=Xs(L,"onBeforeInput"),0<Ee.length&&(Me=new Td("onBeforeInput","beforeinput",null,n,$),Q.push({event:Me,listeners:Ee}),Me.data=pe)),$v(Q,e,L,n,$)}mp(Q,t)})}function vr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Xs(e,t){for(var n=t+"Capture",i=[];e!==null;){var u=e,c=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||c===null||(u=Li(e,n),u!=null&&i.unshift(vr(e,u,c)),u=Li(e,t),u!=null&&i.push(vr(e,u,c))),e.tag===3)return i;e=e.return}return[]}function mi(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function yp(e,t,n,i,u){for(var c=t._reactName,y=[];n!==null&&n!==i;){var b=n,A=b.alternate,L=b.stateNode;if(b=b.tag,A!==null&&A===i)break;b!==5&&b!==26&&b!==27||L===null||(A=L,u?(L=Li(n,c),L!=null&&y.unshift(vr(n,L,A))):u||(L=Li(n,c),L!=null&&y.push(vr(n,L,A)))),n=n.return}y.length!==0&&e.push({event:t,listeners:y})}var Kv=/\r\n?/g,Jv=/\u0000|\uFFFD/g;function gp(e){return(typeof e=="string"?e:""+e).replace(Kv,`
`).replace(Jv,"")}function vp(e,t){return t=gp(t),gp(e)===t}function Zs(){}function Xe(e,t,n,i,u,c){switch(n){case"children":typeof i=="string"?t==="body"||t==="textarea"&&i===""||qa(e,i):(typeof i=="number"||typeof i=="bigint")&&t!=="body"&&qa(e,""+i);break;case"className":Wr(e,"class",i);break;case"tabIndex":Wr(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":Wr(e,n,i);break;case"style":Sd(e,i,c);break;case"data":if(t!=="object"){Wr(e,"data",i);break}case"src":case"href":if(i===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=ts(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Xe(e,t,"name",u.name,u,null),Xe(e,t,"formEncType",u.formEncType,u,null),Xe(e,t,"formMethod",u.formMethod,u,null),Xe(e,t,"formTarget",u.formTarget,u,null)):(Xe(e,t,"encType",u.encType,u,null),Xe(e,t,"method",u.method,u,null),Xe(e,t,"target",u.target,u,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=ts(""+i),e.setAttribute(n,i);break;case"onClick":i!=null&&(e.onclick=Zs);break;case"onScroll":i!=null&&ze("scroll",e);break;case"onScrollEnd":i!=null&&ze("scrollend",e);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(s(61));if(n=i.__html,n!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":e.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){e.removeAttribute("xlink:href");break}n=ts(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":i===!0?e.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":ze("beforetoggle",e),ze("toggle",e),Bl(e,"popover",i);break;case"xlinkActuate":pl(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":pl(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":pl(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":pl(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":pl(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":pl(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":pl(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":pl(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":pl(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":Bl(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Tg.get(n)||n,Bl(e,n,i))}}function Bc(e,t,n,i,u,c){switch(n){case"style":Sd(e,i,c);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(s(61));if(n=i.__html,n!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"children":typeof i=="string"?qa(e,i):(typeof i=="number"||typeof i=="bigint")&&qa(e,""+i);break;case"onScroll":i!=null&&ze("scroll",e);break;case"onScrollEnd":i!=null&&ze("scrollend",e);break;case"onClick":i!=null&&(e.onclick=Zs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!zi.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(u=n.endsWith("Capture"),t=n.slice(2,u?n.length-7:void 0),c=e[me]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,u),typeof i=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,u);break e}n in e?e[n]=i:i===!0?e.setAttribute(n,""):Bl(e,n,i)}}}function Mt(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ze("error",e),ze("load",e);var i=!1,u=!1,c;for(c in n)if(n.hasOwnProperty(c)){var y=n[c];if(y!=null)switch(c){case"src":i=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Xe(e,t,c,y,n,null)}}u&&Xe(e,t,"srcSet",n.srcSet,n,null),i&&Xe(e,t,"src",n.src,n,null);return;case"input":ze("invalid",e);var b=c=y=u=null,A=null,L=null;for(i in n)if(n.hasOwnProperty(i)){var $=n[i];if($!=null)switch(i){case"name":u=$;break;case"type":y=$;break;case"checked":A=$;break;case"defaultChecked":L=$;break;case"value":c=$;break;case"defaultValue":b=$;break;case"children":case"dangerouslySetInnerHTML":if($!=null)throw Error(s(137,t));break;default:Xe(e,t,i,$,n,null)}}yd(e,c,b,A,L,y,u,!1),Ir(e);return;case"select":ze("invalid",e),i=y=c=null;for(u in n)if(n.hasOwnProperty(u)&&(b=n[u],b!=null))switch(u){case"value":c=b;break;case"defaultValue":y=b;break;case"multiple":i=b;default:Xe(e,t,u,b,n,null)}t=c,n=y,e.multiple=!!i,t!=null?Ha(e,!!i,t,!1):n!=null&&Ha(e,!!i,n,!0);return;case"textarea":ze("invalid",e),c=u=i=null;for(y in n)if(n.hasOwnProperty(y)&&(b=n[y],b!=null))switch(y){case"value":i=b;break;case"defaultValue":u=b;break;case"children":c=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(s(91));break;default:Xe(e,t,y,b,n,null)}vd(e,i,u,c),Ir(e);return;case"option":for(A in n)if(n.hasOwnProperty(A)&&(i=n[A],i!=null))switch(A){case"selected":e.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Xe(e,t,A,i,n,null)}return;case"dialog":ze("beforetoggle",e),ze("toggle",e),ze("cancel",e),ze("close",e);break;case"iframe":case"object":ze("load",e);break;case"video":case"audio":for(i=0;i<gr.length;i++)ze(gr[i],e);break;case"image":ze("error",e),ze("load",e);break;case"details":ze("toggle",e);break;case"embed":case"source":case"link":ze("error",e),ze("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(L in n)if(n.hasOwnProperty(L)&&(i=n[L],i!=null))switch(L){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Xe(e,t,L,i,n,null)}return;default:if(Wu(t)){for($ in n)n.hasOwnProperty($)&&(i=n[$],i!==void 0&&Bc(e,t,$,i,n,void 0));return}}for(b in n)n.hasOwnProperty(b)&&(i=n[b],i!=null&&Xe(e,t,b,i,n,null))}function Pv(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,c=null,y=null,b=null,A=null,L=null,$=null;for(q in n){var Q=n[q];if(n.hasOwnProperty(q)&&Q!=null)switch(q){case"checked":break;case"value":break;case"defaultValue":A=Q;default:i.hasOwnProperty(q)||Xe(e,t,q,null,i,Q)}}for(var k in i){var q=i[k];if(Q=n[k],i.hasOwnProperty(k)&&(q!=null||Q!=null))switch(k){case"type":c=q;break;case"name":u=q;break;case"checked":L=q;break;case"defaultChecked":$=q;break;case"value":y=q;break;case"defaultValue":b=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(s(137,t));break;default:q!==Q&&Xe(e,t,k,q,i,Q)}}Ju(e,y,b,A,L,$,c,u);return;case"select":q=y=b=k=null;for(c in n)if(A=n[c],n.hasOwnProperty(c)&&A!=null)switch(c){case"value":break;case"multiple":q=A;default:i.hasOwnProperty(c)||Xe(e,t,c,null,i,A)}for(u in i)if(c=i[u],A=n[u],i.hasOwnProperty(u)&&(c!=null||A!=null))switch(u){case"value":k=c;break;case"defaultValue":b=c;break;case"multiple":y=c;default:c!==A&&Xe(e,t,u,c,i,A)}t=b,n=y,i=q,k!=null?Ha(e,!!n,k,!1):!!i!=!!n&&(t!=null?Ha(e,!!n,t,!0):Ha(e,!!n,n?[]:"",!1));return;case"textarea":q=k=null;for(b in n)if(u=n[b],n.hasOwnProperty(b)&&u!=null&&!i.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:Xe(e,t,b,null,i,u)}for(y in i)if(u=i[y],c=n[y],i.hasOwnProperty(y)&&(u!=null||c!=null))switch(y){case"value":k=u;break;case"defaultValue":q=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(s(91));break;default:u!==c&&Xe(e,t,y,u,i,c)}gd(e,k,q);return;case"option":for(var Ae in n)if(k=n[Ae],n.hasOwnProperty(Ae)&&k!=null&&!i.hasOwnProperty(Ae))switch(Ae){case"selected":e.selected=!1;break;default:Xe(e,t,Ae,null,i,k)}for(A in i)if(k=i[A],q=n[A],i.hasOwnProperty(A)&&k!==q&&(k!=null||q!=null))switch(A){case"selected":e.selected=k&&typeof k!="function"&&typeof k!="symbol";break;default:Xe(e,t,A,k,i,q)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var Se in n)k=n[Se],n.hasOwnProperty(Se)&&k!=null&&!i.hasOwnProperty(Se)&&Xe(e,t,Se,null,i,k);for(L in i)if(k=i[L],q=n[L],i.hasOwnProperty(L)&&k!==q&&(k!=null||q!=null))switch(L){case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(s(137,t));break;default:Xe(e,t,L,k,i,q)}return;default:if(Wu(t)){for(var Ze in n)k=n[Ze],n.hasOwnProperty(Ze)&&k!==void 0&&!i.hasOwnProperty(Ze)&&Bc(e,t,Ze,void 0,i,k);for($ in i)k=i[$],q=n[$],!i.hasOwnProperty($)||k===q||k===void 0&&q===void 0||Bc(e,t,$,k,i,q);return}}for(var U in n)k=n[U],n.hasOwnProperty(U)&&k!=null&&!i.hasOwnProperty(U)&&Xe(e,t,U,null,i,k);for(Q in i)k=i[Q],q=n[Q],!i.hasOwnProperty(Q)||k===q||k==null&&q==null||Xe(e,t,Q,k,i,q)}var kc=null,Hc=null;function Qs(e){return e.nodeType===9?e:e.ownerDocument}function bp(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Sp(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function qc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Vc=null;function Wv(){var e=window.event;return e&&e.type==="popstate"?e===Vc?!1:(Vc=e,!0):(Vc=null,!1)}var xp=typeof setTimeout=="function"?setTimeout:void 0,Iv=typeof clearTimeout=="function"?clearTimeout:void 0,Ep=typeof Promise=="function"?Promise:void 0,eb=typeof queueMicrotask=="function"?queueMicrotask:typeof Ep<"u"?function(e){return Ep.resolve(null).then(e).catch(tb)}:xp;function tb(e){setTimeout(function(){throw e})}function ea(e){return e==="head"}function wp(e,t){var n=t,i=0,u=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<i&&8>i){n=i;var y=e.ownerDocument;if(n&1&&br(y.documentElement),n&2&&br(y.body),n&4)for(n=y.head,br(n),y=n.firstChild;y;){var b=y.nextSibling,A=y.nodeName;y[_t]||A==="SCRIPT"||A==="STYLE"||A==="LINK"&&y.rel.toLowerCase()==="stylesheet"||n.removeChild(y),y=b}}if(u===0){e.removeChild(c),Rr(t);return}u--}else n==="$"||n==="$?"||n==="$!"?u++:i=n.charCodeAt(0)-48;else i=0;n=c}while(n);Rr(t)}function Fc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Fc(n),Ge(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function nb(e,t,n,i){for(;e.nodeType===1;){var u=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(i){if(!e[_t])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Mn(e.nextSibling),e===null)break}return null}function lb(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Mn(e.nextSibling),e===null))return null;return e}function Yc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function ab(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var i=function(){t(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}function Mn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Gc=null;function Ap(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function _p(e,t,n){switch(t=Qs(n),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function br(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ge(e)}var _n=new Map,Tp=new Set;function Ks(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var jl=J.d;J.d={f:ib,r:rb,D:sb,C:ub,L:ob,m:cb,X:db,S:fb,M:hb};function ib(){var e=jl.f(),t=qs();return e||t}function rb(e){var t=zl(e);t!==null&&t.tag===5&&t.type==="form"?Xh(t):jl.r(e)}var pi=typeof document>"u"?null:document;function Rp(e,t,n){var i=pi;if(i&&typeof t=="string"&&t){var u=vn(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof n=="string"&&(u+='[crossorigin="'+n+'"]'),Tp.has(u)||(Tp.add(u),e={rel:e,crossOrigin:n,href:t},i.querySelector(u)===null&&(t=i.createElement("link"),Mt(t,"link",e),ot(t),i.head.appendChild(t)))}}function sb(e){jl.D(e),Rp("dns-prefetch",e,null)}function ub(e,t){jl.C(e,t),Rp("preconnect",e,t)}function ob(e,t,n){jl.L(e,t,n);var i=pi;if(i&&e&&t){var u='link[rel="preload"][as="'+vn(t)+'"]';t==="image"&&n&&n.imageSrcSet?(u+='[imagesrcset="'+vn(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(u+='[imagesizes="'+vn(n.imageSizes)+'"]')):u+='[href="'+vn(e)+'"]';var c=u;switch(t){case"style":c=yi(e);break;case"script":c=gi(e)}_n.has(c)||(e=g({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),_n.set(c,e),i.querySelector(u)!==null||t==="style"&&i.querySelector(Sr(c))||t==="script"&&i.querySelector(xr(c))||(t=i.createElement("link"),Mt(t,"link",e),ot(t),i.head.appendChild(t)))}}function cb(e,t){jl.m(e,t);var n=pi;if(n&&e){var i=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+vn(i)+'"][href="'+vn(e)+'"]',c=u;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=gi(e)}if(!_n.has(c)&&(e=g({rel:"modulepreload",href:e},t),_n.set(c,e),n.querySelector(u)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(xr(c)))return}i=n.createElement("link"),Mt(i,"link",e),ot(i),n.head.appendChild(i)}}}function fb(e,t,n){jl.S(e,t,n);var i=pi;if(i&&e){var u=ml(i).hoistableStyles,c=yi(e);t=t||"default";var y=u.get(c);if(!y){var b={loading:0,preload:null};if(y=i.querySelector(Sr(c)))b.loading=5;else{e=g({rel:"stylesheet",href:e,"data-precedence":t},n),(n=_n.get(c))&&$c(e,n);var A=y=i.createElement("link");ot(A),Mt(A,"link",e),A._p=new Promise(function(L,$){A.onload=L,A.onerror=$}),A.addEventListener("load",function(){b.loading|=1}),A.addEventListener("error",function(){b.loading|=2}),b.loading|=4,Js(y,t,i)}y={type:"stylesheet",instance:y,count:1,state:b},u.set(c,y)}}}function db(e,t){jl.X(e,t);var n=pi;if(n&&e){var i=ml(n).hoistableScripts,u=gi(e),c=i.get(u);c||(c=n.querySelector(xr(u)),c||(e=g({src:e,async:!0},t),(t=_n.get(u))&&Xc(e,t),c=n.createElement("script"),ot(c),Mt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(u,c))}}function hb(e,t){jl.M(e,t);var n=pi;if(n&&e){var i=ml(n).hoistableScripts,u=gi(e),c=i.get(u);c||(c=n.querySelector(xr(u)),c||(e=g({src:e,async:!0,type:"module"},t),(t=_n.get(u))&&Xc(e,t),c=n.createElement("script"),ot(c),Mt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(u,c))}}function Op(e,t,n,i){var u=(u=xe.current)?Ks(u):null;if(!u)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=yi(n.href),n=ml(u).hoistableStyles,i=n.get(t),i||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=yi(n.href);var c=ml(u).hoistableStyles,y=c.get(e);if(y||(u=u.ownerDocument||u,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,y),(c=u.querySelector(Sr(e)))&&!c._p&&(y.instance=c,y.state.loading=5),_n.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},_n.set(e,n),c||mb(u,e,n,y.state))),t&&i===null)throw Error(s(528,""));return y}if(t&&i!==null)throw Error(s(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=gi(n),n=ml(u).hoistableScripts,i=n.get(t),i||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function yi(e){return'href="'+vn(e)+'"'}function Sr(e){return'link[rel="stylesheet"]['+e+"]"}function jp(e){return g({},e,{"data-precedence":e.precedence,precedence:null})}function mb(e,t,n,i){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?i.loading=1:(t=e.createElement("link"),i.preload=t,t.addEventListener("load",function(){return i.loading|=1}),t.addEventListener("error",function(){return i.loading|=2}),Mt(t,"link",n),ot(t),e.head.appendChild(t))}function gi(e){return'[src="'+vn(e)+'"]'}function xr(e){return"script[async]"+e}function Dp(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+vn(n.href)+'"]');if(i)return t.instance=i,ot(i),i;var u=g({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(e.ownerDocument||e).createElement("style"),ot(i),Mt(i,"style",u),Js(i,n.precedence,e),t.instance=i;case"stylesheet":u=yi(n.href);var c=e.querySelector(Sr(u));if(c)return t.state.loading|=4,t.instance=c,ot(c),c;i=jp(n),(u=_n.get(u))&&$c(i,u),c=(e.ownerDocument||e).createElement("link"),ot(c);var y=c;return y._p=new Promise(function(b,A){y.onload=b,y.onerror=A}),Mt(c,"link",i),t.state.loading|=4,Js(c,n.precedence,e),t.instance=c;case"script":return c=gi(n.src),(u=e.querySelector(xr(c)))?(t.instance=u,ot(u),u):(i=n,(u=_n.get(c))&&(i=g({},n),Xc(i,u)),e=e.ownerDocument||e,u=e.createElement("script"),ot(u),Mt(u,"link",i),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(i=t.instance,t.state.loading|=4,Js(i,n.precedence,e));return t.instance}function Js(e,t,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=i.length?i[i.length-1]:null,c=u,y=0;y<i.length;y++){var b=i[y];if(b.dataset.precedence===t)c=b;else if(c!==u)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function $c(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Xc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ps=null;function Cp(e,t,n){if(Ps===null){var i=new Map,u=Ps=new Map;u.set(n,i)}else u=Ps,i=u.get(n),i||(i=new Map,u.set(n,i));if(i.has(e))return i;for(i.set(e,null),n=n.getElementsByTagName(e),u=0;u<n.length;u++){var c=n[u];if(!(c[_t]||c[ue]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var y=c.getAttribute(t)||"";y=e+y;var b=i.get(y);b?b.push(c):i.set(y,[c])}}return i}function Np(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function pb(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Mp(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Er=null;function yb(){}function gb(e,t,n){if(Er===null)throw Error(s(475));var i=Er;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=yi(n.href),c=e.querySelector(Sr(u));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(i.count++,i=Ws.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=c,ot(c);return}c=e.ownerDocument||e,n=jp(n),(u=_n.get(u))&&$c(n,u),c=c.createElement("link"),ot(c);var y=c;y._p=new Promise(function(b,A){y.onload=b,y.onerror=A}),Mt(c,"link",n),t.instance=c}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(i.count++,t=Ws.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}function vb(){if(Er===null)throw Error(s(475));var e=Er;return e.stylesheets&&e.count===0&&Zc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Zc(e,e.stylesheets),e.unsuspend){var i=e.unsuspend;e.unsuspend=null,i()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Ws(){if(this.count--,this.count===0){if(this.stylesheets)Zc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Is=null;function Zc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Is=new Map,t.forEach(bb,e),Is=null,Ws.call(e))}function bb(e,t){if(!(t.state.loading&4)){var n=Is.get(e);if(n)var i=n.get(null);else{n=new Map,Is.set(e,n);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<u.length;c++){var y=u[c];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(n.set(y.dataset.precedence,y),i=y)}i&&n.set(null,i)}u=t.instance,y=u.getAttribute("data-precedence"),c=n.get(y)||i,c===i&&n.set(null,u),n.set(y,u),this.count++,i=Ws.bind(this),u.addEventListener("load",i),u.addEventListener("error",i),c?c.parentNode.insertBefore(u,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var wr={$$typeof:M,Provider:null,Consumer:null,_currentValue:te,_currentValue2:te,_threadCount:0};function Sb(e,t,n,i,u,c,y,b){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=$n(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=$n(0),this.hiddenUpdates=$n(null),this.identifierPrefix=i,this.onUncaughtError=u,this.onCaughtError=c,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function Up(e,t,n,i,u,c,y,b,A,L,$,Q){return e=new Sb(e,t,n,y,b,A,L,Q),t=1,c===!0&&(t|=24),c=sn(3,null,null,t),e.current=c,c.stateNode=e,t=Oo(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:i,isDehydrated:n,cache:t},No(c),e}function zp(e){return e?(e=Qa,e):Qa}function Lp(e,t,n,i,u,c){u=zp(u),i.context===null?i.context=u:i.pendingContext=u,i=Vl(t),i.payload={element:n},c=c===void 0?null:c,c!==null&&(i.callback=c),n=Fl(e,i,t),n!==null&&(dn(n,e,t),Ii(n,e,t))}function Bp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Qc(e,t){Bp(e,t),(e=e.alternate)&&Bp(e,t)}function kp(e){if(e.tag===13){var t=Za(e,67108864);t!==null&&dn(t,e,67108864),Qc(e,67108864)}}var eu=!0;function xb(e,t,n,i){var u=H.T;H.T=null;var c=J.p;try{J.p=2,Kc(e,t,n,i)}finally{J.p=c,H.T=u}}function Eb(e,t,n,i){var u=H.T;H.T=null;var c=J.p;try{J.p=8,Kc(e,t,n,i)}finally{J.p=c,H.T=u}}function Kc(e,t,n,i){if(eu){var u=Jc(i);if(u===null)Lc(e,t,i,tu,n),qp(e,i);else if(Ab(u,e,t,n,i))i.stopPropagation();else if(qp(e,i),t&4&&-1<wb.indexOf(e)){for(;u!==null;){var c=zl(u);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var y=$t(c.pendingLanes);if(y!==0){var b=c;for(b.pendingLanes|=2,b.entangledLanes|=2;y;){var A=1<<31-ve(y);b.entanglements[1]|=A,y&=~A}Pn(c),(Ve&6)===0&&(ks=st()+500,yr(0))}}break;case 13:b=Za(c,2),b!==null&&dn(b,c,2),qs(),Qc(c,2)}if(c=Jc(i),c===null&&Lc(e,t,i,tu,n),c===u)break;u=c}u!==null&&i.stopPropagation()}else Lc(e,t,i,null,n)}}function Jc(e){return e=eo(e),Pc(e)}var tu=null;function Pc(e){if(tu=null,e=an(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return tu=e,null}function Hp(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Rn()){case Ye:return 2;case cl:return 8;case nn:case j:return 32;case B:return 268435456;default:return 32}default:return 32}}var Wc=!1,ta=null,na=null,la=null,Ar=new Map,_r=new Map,aa=[],wb="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function qp(e,t){switch(e){case"focusin":case"focusout":ta=null;break;case"dragenter":case"dragleave":na=null;break;case"mouseover":case"mouseout":la=null;break;case"pointerover":case"pointerout":Ar.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":_r.delete(t.pointerId)}}function Tr(e,t,n,i,u,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:c,targetContainers:[u]},t!==null&&(t=zl(t),t!==null&&kp(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function Ab(e,t,n,i,u){switch(t){case"focusin":return ta=Tr(ta,e,t,n,i,u),!0;case"dragenter":return na=Tr(na,e,t,n,i,u),!0;case"mouseover":return la=Tr(la,e,t,n,i,u),!0;case"pointerover":var c=u.pointerId;return Ar.set(c,Tr(Ar.get(c)||null,e,t,n,i,u)),!0;case"gotpointercapture":return c=u.pointerId,_r.set(c,Tr(_r.get(c)||null,e,t,n,i,u)),!0}return!1}function Vp(e){var t=an(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,hl(e.priority,function(){if(n.tag===13){var i=fn();i=be(i);var u=Za(n,i);u!==null&&dn(u,n,i),Qc(n,i)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function nu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jc(e.nativeEvent);if(n===null){n=e.nativeEvent;var i=new n.constructor(n.type,n);Iu=i,n.target.dispatchEvent(i),Iu=null}else return t=zl(n),t!==null&&kp(t),e.blockedOn=n,!1;t.shift()}return!0}function Fp(e,t,n){nu(e)&&n.delete(t)}function _b(){Wc=!1,ta!==null&&nu(ta)&&(ta=null),na!==null&&nu(na)&&(na=null),la!==null&&nu(la)&&(la=null),Ar.forEach(Fp),_r.forEach(Fp)}function lu(e,t){e.blockedOn===t&&(e.blockedOn=null,Wc||(Wc=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,_b)))}var au=null;function Yp(e){au!==e&&(au=e,l.unstable_scheduleCallback(l.unstable_NormalPriority,function(){au===e&&(au=null);for(var t=0;t<e.length;t+=3){var n=e[t],i=e[t+1],u=e[t+2];if(typeof i!="function"){if(Pc(i||n)===null)continue;break}var c=zl(n);c!==null&&(e.splice(t,3),t-=3,Po(c,{pending:!0,data:u,method:n.method,action:i},i,u))}}))}function Rr(e){function t(A){return lu(A,e)}ta!==null&&lu(ta,e),na!==null&&lu(na,e),la!==null&&lu(la,e),Ar.forEach(t),_r.forEach(t);for(var n=0;n<aa.length;n++){var i=aa[n];i.blockedOn===e&&(i.blockedOn=null)}for(;0<aa.length&&(n=aa[0],n.blockedOn===null);)Vp(n),n.blockedOn===null&&aa.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var u=n[i],c=n[i+1],y=u[me]||null;if(typeof c=="function")y||Yp(n);else if(y){var b=null;if(c&&c.hasAttribute("formAction")){if(u=c,y=c[me]||null)b=y.formAction;else if(Pc(u)!==null)continue}else b=y.action;typeof b=="function"?n[i+1]=b:(n.splice(i,3),i-=3),Yp(n)}}}function Ic(e){this._internalRoot=e}iu.prototype.render=Ic.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var n=t.current,i=fn();Lp(n,i,e,t,null,null)},iu.prototype.unmount=Ic.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Lp(e.current,2,null,e,null,null),qs(),t[We]=null}};function iu(e){this._internalRoot=e}iu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ut();e={blockedOn:null,target:e,priority:t};for(var n=0;n<aa.length&&t!==0&&t<aa[n].priority;n++);aa.splice(n,0,e),n===0&&Vp(e)}};var Gp=a.version;if(Gp!=="19.1.0")throw Error(s(527,Gp,"19.1.0"));J.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=p(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var Tb={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:H,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{P=ru.inject(Tb),K=ru}catch{}}return jr.createRoot=function(e,t){if(!o(e))throw Error(s(299));var n=!1,i="",u=rm,c=sm,y=um,b=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(y=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(b=t.unstable_transitionCallbacks)),t=Up(e,1,!1,null,null,n,i,u,c,y,b,null),e[We]=t.current,zc(e),new Ic(t)},jr.hydrateRoot=function(e,t,n){if(!o(e))throw Error(s(299));var i=!1,u="",c=rm,y=sm,b=um,A=null,L=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(y=n.onCaughtError),n.onRecoverableError!==void 0&&(b=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(A=n.unstable_transitionCallbacks),n.formState!==void 0&&(L=n.formState)),t=Up(e,1,!0,t,n??null,i,u,c,y,b,A,L),t.context=zp(null),n=t.current,i=fn(),i=be(i),u=Vl(i),u.callback=null,Fl(n,u,i),n=i,t.current.lanes=n,jn(t,n),Pn(t),e[We]=t.current,zc(e),new iu(t)},jr.version="19.1.0",jr}var e0;function Lb(){if(e0)return nf.exports;e0=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(a){console.error(a)}}return l(),nf.exports=zb(),nf.exports}var Bb=Lb(),Dr={},t0;function kb(){if(t0)return Dr;t0=1,Object.defineProperty(Dr,"__esModule",{value:!0}),Dr.parse=d,Dr.serialize=m;const l=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,a=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,f=(()=>{const x=function(){};return x.prototype=Object.create(null),x})();function d(x,R){const E=new f,C=x.length;if(C<2)return E;const D=(R==null?void 0:R.decode)||g;let w=0;do{const T=x.indexOf("=",w);if(T===-1)break;const M=x.indexOf(";",w),X=M===-1?C:M;if(T>X){w=x.lastIndexOf(";",T-1)+1;continue}const V=h(x,w,T),I=p(x,T,V),W=x.slice(V,I);if(E[W]===void 0){let se=h(x,T+1,X),fe=p(x,X,se);const De=D(x.slice(se,fe));E[W]=De}w=X+1}while(w<C);return E}function h(x,R,E){do{const C=x.charCodeAt(R);if(C!==32&&C!==9)return R}while(++R<E);return E}function p(x,R,E){for(;R>E;){const C=x.charCodeAt(--R);if(C!==32&&C!==9)return R+1}return E}function m(x,R,E){const C=(E==null?void 0:E.encode)||encodeURIComponent;if(!l.test(x))throw new TypeError(`argument name is invalid: ${x}`);const D=C(R);if(!a.test(D))throw new TypeError(`argument val is invalid: ${R}`);let w=x+"="+D;if(!E)return w;if(E.maxAge!==void 0){if(!Number.isInteger(E.maxAge))throw new TypeError(`option maxAge is invalid: ${E.maxAge}`);w+="; Max-Age="+E.maxAge}if(E.domain){if(!r.test(E.domain))throw new TypeError(`option domain is invalid: ${E.domain}`);w+="; Domain="+E.domain}if(E.path){if(!s.test(E.path))throw new TypeError(`option path is invalid: ${E.path}`);w+="; Path="+E.path}if(E.expires){if(!S(E.expires)||!Number.isFinite(E.expires.valueOf()))throw new TypeError(`option expires is invalid: ${E.expires}`);w+="; Expires="+E.expires.toUTCString()}if(E.httpOnly&&(w+="; HttpOnly"),E.secure&&(w+="; Secure"),E.partitioned&&(w+="; Partitioned"),E.priority)switch(typeof E.priority=="string"?E.priority.toLowerCase():void 0){case"low":w+="; Priority=Low";break;case"medium":w+="; Priority=Medium";break;case"high":w+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${E.priority}`)}if(E.sameSite)switch(typeof E.sameSite=="string"?E.sameSite.toLowerCase():E.sameSite){case!0:case"strict":w+="; SameSite=Strict";break;case"lax":w+="; SameSite=Lax";break;case"none":w+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${E.sameSite}`)}return w}function g(x){if(x.indexOf("%")===-1)return x;try{return decodeURIComponent(x)}catch{return x}}function S(x){return o.call(x)==="[object Date]"}return Dr}kb();var n0="popstate";function Hb(l={}){function a(s,o){let{pathname:f,search:d,hash:h}=s.location;return Af("",{pathname:f,search:d,hash:h},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function r(s,o){return typeof o=="string"?o:kr(o)}return Vb(a,r,null,l)}function nt(l,a){if(l===!1||l===null||typeof l>"u")throw new Error(a)}function Ln(l,a){if(!l){typeof console<"u"&&console.warn(a);try{throw new Error(a)}catch{}}}function qb(){return Math.random().toString(36).substring(2,10)}function l0(l,a){return{usr:l.state,key:l.key,idx:a}}function Af(l,a,r=null,s){return{pathname:typeof l=="string"?l:l.pathname,search:"",hash:"",...typeof a=="string"?Ti(a):a,state:r,key:a&&a.key||s||qb()}}function kr({pathname:l="/",search:a="",hash:r=""}){return a&&a!=="?"&&(l+=a.charAt(0)==="?"?a:"?"+a),r&&r!=="#"&&(l+=r.charAt(0)==="#"?r:"#"+r),l}function Ti(l){let a={};if(l){let r=l.indexOf("#");r>=0&&(a.hash=l.substring(r),l=l.substring(0,r));let s=l.indexOf("?");s>=0&&(a.search=l.substring(s),l=l.substring(0,s)),l&&(a.pathname=l)}return a}function Vb(l,a,r,s={}){let{window:o=document.defaultView,v5Compat:f=!1}=s,d=o.history,h="POP",p=null,m=g();m==null&&(m=0,d.replaceState({...d.state,idx:m},""));function g(){return(d.state||{idx:null}).idx}function S(){h="POP";let D=g(),w=D==null?null:D-m;m=D,p&&p({action:h,location:C.location,delta:w})}function x(D,w){h="PUSH";let T=Af(C.location,D,w);m=g()+1;let M=l0(T,m),X=C.createHref(T);try{d.pushState(M,"",X)}catch(V){if(V instanceof DOMException&&V.name==="DataCloneError")throw V;o.location.assign(X)}f&&p&&p({action:h,location:C.location,delta:1})}function R(D,w){h="REPLACE";let T=Af(C.location,D,w);m=g();let M=l0(T,m),X=C.createHref(T);d.replaceState(M,"",X),f&&p&&p({action:h,location:C.location,delta:0})}function E(D){let w=o.location.origin!=="null"?o.location.origin:o.location.href,T=typeof D=="string"?D:kr(D);return T=T.replace(/ $/,"%20"),nt(w,`No window.location.(origin|href) available to create URL for href: ${T}`),new URL(T,w)}let C={get action(){return h},get location(){return l(o,d)},listen(D){if(p)throw new Error("A history only accepts one active listener");return o.addEventListener(n0,S),p=D,()=>{o.removeEventListener(n0,S),p=null}},createHref(D){return a(o,D)},createURL:E,encodeLocation(D){let w=E(D);return{pathname:w.pathname,search:w.search,hash:w.hash}},push:x,replace:R,go(D){return d.go(D)}};return C}function ny(l,a,r="/"){return Fb(l,a,r,!1)}function Fb(l,a,r,s){let o=typeof a=="string"?Ti(a):a,f=Ul(o.pathname||"/",r);if(f==null)return null;let d=ly(l);Yb(d);let h=null;for(let p=0;h==null&&p<d.length;++p){let m=e1(f);h=Wb(d[p],m,s)}return h}function ly(l,a=[],r=[],s=""){let o=(f,d,h)=>{let p={relativePath:h===void 0?f.path||"":h,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};p.relativePath.startsWith("/")&&(nt(p.relativePath.startsWith(s),`Absolute route path "${p.relativePath}" nested under path "${s}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(s.length));let m=Nl([s,p.relativePath]),g=r.concat(p);f.children&&f.children.length>0&&(nt(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),ly(f.children,a,g,m)),!(f.path==null&&!f.index)&&a.push({path:m,score:Jb(m,f.index),routesMeta:g})};return l.forEach((f,d)=>{var h;if(f.path===""||!((h=f.path)!=null&&h.includes("?")))o(f,d);else for(let p of ay(f.path))o(f,d,p)}),a}function ay(l){let a=l.split("/");if(a.length===0)return[];let[r,...s]=a,o=r.endsWith("?"),f=r.replace(/\?$/,"");if(s.length===0)return o?[f,""]:[f];let d=ay(s.join("/")),h=[];return h.push(...d.map(p=>p===""?f:[f,p].join("/"))),o&&h.push(...d),h.map(p=>l.startsWith("/")&&p===""?"/":p)}function Yb(l){l.sort((a,r)=>a.score!==r.score?r.score-a.score:Pb(a.routesMeta.map(s=>s.childrenIndex),r.routesMeta.map(s=>s.childrenIndex)))}var Gb=/^:[\w-]+$/,$b=3,Xb=2,Zb=1,Qb=10,Kb=-2,a0=l=>l==="*";function Jb(l,a){let r=l.split("/"),s=r.length;return r.some(a0)&&(s+=Kb),a&&(s+=Xb),r.filter(o=>!a0(o)).reduce((o,f)=>o+(Gb.test(f)?$b:f===""?Zb:Qb),s)}function Pb(l,a){return l.length===a.length&&l.slice(0,-1).every((s,o)=>s===a[o])?l[l.length-1]-a[a.length-1]:0}function Wb(l,a,r=!1){let{routesMeta:s}=l,o={},f="/",d=[];for(let h=0;h<s.length;++h){let p=s[h],m=h===s.length-1,g=f==="/"?a:a.slice(f.length)||"/",S=bu({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},g),x=p.route;if(!S&&m&&r&&!s[s.length-1].route.index&&(S=bu({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},g)),!S)return null;Object.assign(o,S.params),d.push({params:o,pathname:Nl([f,S.pathname]),pathnameBase:a1(Nl([f,S.pathnameBase])),route:x}),S.pathnameBase!=="/"&&(f=Nl([f,S.pathnameBase]))}return d}function bu(l,a){typeof l=="string"&&(l={path:l,caseSensitive:!1,end:!0});let[r,s]=Ib(l.path,l.caseSensitive,l.end),o=a.match(r);if(!o)return null;let f=o[0],d=f.replace(/(.)\/+$/,"$1"),h=o.slice(1);return{params:s.reduce((m,{paramName:g,isOptional:S},x)=>{if(g==="*"){let E=h[x]||"";d=f.slice(0,f.length-E.length).replace(/(.)\/+$/,"$1")}const R=h[x];return S&&!R?m[g]=void 0:m[g]=(R||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:d,pattern:l}}function Ib(l,a=!1,r=!0){Ln(l==="*"||!l.endsWith("*")||l.endsWith("/*"),`Route path "${l}" will be treated as if it were "${l.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${l.replace(/\*$/,"/*")}".`);let s=[],o="^"+l.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,h,p)=>(s.push({paramName:h,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return l.endsWith("*")?(s.push({paramName:"*"}),o+=l==="*"||l==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?o+="\\/*$":l!==""&&l!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,a?void 0:"i"),s]}function e1(l){try{return l.split("/").map(a=>decodeURIComponent(a).replace(/\//g,"%2F")).join("/")}catch(a){return Ln(!1,`The URL path "${l}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${a}).`),l}}function Ul(l,a){if(a==="/")return l;if(!l.toLowerCase().startsWith(a.toLowerCase()))return null;let r=a.endsWith("/")?a.length-1:a.length,s=l.charAt(r);return s&&s!=="/"?null:l.slice(r)||"/"}function t1(l,a="/"){let{pathname:r,search:s="",hash:o=""}=typeof l=="string"?Ti(l):l;return{pathname:r?r.startsWith("/")?r:n1(r,a):a,search:i1(s),hash:r1(o)}}function n1(l,a){let r=a.replace(/\/+$/,"").split("/");return l.split("/").forEach(o=>{o===".."?r.length>1&&r.pop():o!=="."&&r.push(o)}),r.length>1?r.join("/"):"/"}function sf(l,a,r,s){return`Cannot include a '${l}' character in a manually specified \`to.${a}\` field [${JSON.stringify(s)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function l1(l){return l.filter((a,r)=>r===0||a.route.path&&a.route.path.length>0)}function Ff(l){let a=l1(l);return a.map((r,s)=>s===a.length-1?r.pathname:r.pathnameBase)}function Yf(l,a,r,s=!1){let o;typeof l=="string"?o=Ti(l):(o={...l},nt(!o.pathname||!o.pathname.includes("?"),sf("?","pathname","search",o)),nt(!o.pathname||!o.pathname.includes("#"),sf("#","pathname","hash",o)),nt(!o.search||!o.search.includes("#"),sf("#","search","hash",o)));let f=l===""||o.pathname==="",d=f?"/":o.pathname,h;if(d==null)h=r;else{let S=a.length-1;if(!s&&d.startsWith("..")){let x=d.split("/");for(;x[0]==="..";)x.shift(),S-=1;o.pathname=x.join("/")}h=S>=0?a[S]:"/"}let p=t1(o,h),m=d&&d!=="/"&&d.endsWith("/"),g=(f||d===".")&&r.endsWith("/");return!p.pathname.endsWith("/")&&(m||g)&&(p.pathname+="/"),p}var Nl=l=>l.join("/").replace(/\/\/+/g,"/"),a1=l=>l.replace(/\/+$/,"").replace(/^\/*/,"/"),i1=l=>!l||l==="?"?"":l.startsWith("?")?l:"?"+l,r1=l=>!l||l==="#"?"":l.startsWith("#")?l:"#"+l;function s1(l){return l!=null&&typeof l.status=="number"&&typeof l.statusText=="string"&&typeof l.internal=="boolean"&&"data"in l}var iy=["POST","PUT","PATCH","DELETE"];new Set(iy);var u1=["GET",...iy];new Set(u1);var Ri=_.createContext(null);Ri.displayName="DataRouter";var Nu=_.createContext(null);Nu.displayName="DataRouterState";var ry=_.createContext({isTransitioning:!1});ry.displayName="ViewTransition";var o1=_.createContext(new Map);o1.displayName="Fetchers";var c1=_.createContext(null);c1.displayName="Await";var Hn=_.createContext(null);Hn.displayName="Navigation";var Fr=_.createContext(null);Fr.displayName="Location";var qn=_.createContext({outlet:null,matches:[],isDataRoute:!1});qn.displayName="Route";var Gf=_.createContext(null);Gf.displayName="RouteError";function f1(l,{relative:a}={}){nt(Oi(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:s}=_.useContext(Hn),{hash:o,pathname:f,search:d}=Yr(l,{relative:a}),h=f;return r!=="/"&&(h=f==="/"?r:Nl([r,f])),s.createHref({pathname:h,search:d,hash:o})}function Oi(){return _.useContext(Fr)!=null}function sl(){return nt(Oi(),"useLocation() may be used only in the context of a <Router> component."),_.useContext(Fr).location}var sy="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function uy(l){_.useContext(Hn).static||_.useLayoutEffect(l)}function Vn(){let{isDataRoute:l}=_.useContext(qn);return l?T1():d1()}function d1(){nt(Oi(),"useNavigate() may be used only in the context of a <Router> component.");let l=_.useContext(Ri),{basename:a,navigator:r}=_.useContext(Hn),{matches:s}=_.useContext(qn),{pathname:o}=sl(),f=JSON.stringify(Ff(s)),d=_.useRef(!1);return uy(()=>{d.current=!0}),_.useCallback((p,m={})=>{if(Ln(d.current,sy),!d.current)return;if(typeof p=="number"){r.go(p);return}let g=Yf(p,JSON.parse(f),o,m.relative==="path");l==null&&a!=="/"&&(g.pathname=g.pathname==="/"?a:Nl([a,g.pathname])),(m.replace?r.replace:r.push)(g,m.state,m)},[a,r,f,o,l])}var h1=_.createContext(null);function m1(l){let a=_.useContext(qn).outlet;return a&&_.createElement(h1.Provider,{value:l},a)}function Yr(l,{relative:a}={}){let{matches:r}=_.useContext(qn),{pathname:s}=sl(),o=JSON.stringify(Ff(r));return _.useMemo(()=>Yf(l,JSON.parse(o),s,a==="path"),[l,o,s,a])}function p1(l,a){return oy(l,a)}function oy(l,a,r,s){var T;nt(Oi(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o,static:f}=_.useContext(Hn),{matches:d}=_.useContext(qn),h=d[d.length-1],p=h?h.params:{},m=h?h.pathname:"/",g=h?h.pathnameBase:"/",S=h&&h.route;{let M=S&&S.path||"";cy(m,!S||M.endsWith("*")||M.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${M}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${M}"> to <Route path="${M==="/"?"*":`${M}/*`}">.`)}let x=sl(),R;if(a){let M=typeof a=="string"?Ti(a):a;nt(g==="/"||((T=M.pathname)==null?void 0:T.startsWith(g)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${g}" but pathname "${M.pathname}" was given in the \`location\` prop.`),R=M}else R=x;let E=R.pathname||"/",C=E;if(g!=="/"){let M=g.replace(/^\//,"").split("/");C="/"+E.replace(/^\//,"").split("/").slice(M.length).join("/")}let D=!f&&r&&r.matches&&r.matches.length>0?r.matches:ny(l,{pathname:C});Ln(S||D!=null,`No routes matched location "${R.pathname}${R.search}${R.hash}" `),Ln(D==null||D[D.length-1].route.element!==void 0||D[D.length-1].route.Component!==void 0||D[D.length-1].route.lazy!==void 0,`Matched leaf route at location "${R.pathname}${R.search}${R.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let w=S1(D&&D.map(M=>Object.assign({},M,{params:Object.assign({},p,M.params),pathname:Nl([g,o.encodeLocation?o.encodeLocation(M.pathname).pathname:M.pathname]),pathnameBase:M.pathnameBase==="/"?g:Nl([g,o.encodeLocation?o.encodeLocation(M.pathnameBase).pathname:M.pathnameBase])})),d,r,s);return a&&w?_.createElement(Fr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...R},navigationType:"POP"}},w):w}function y1(){let l=_1(),a=s1(l)?`${l.status} ${l.statusText}`:l instanceof Error?l.message:JSON.stringify(l),r=l instanceof Error?l.stack:null,s="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:s},f={padding:"2px 4px",backgroundColor:s},d=null;return console.error("Error handled by React Router default ErrorBoundary:",l),d=_.createElement(_.Fragment,null,_.createElement("p",null,"💿 Hey developer 👋"),_.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",_.createElement("code",{style:f},"ErrorBoundary")," or"," ",_.createElement("code",{style:f},"errorElement")," prop on your route.")),_.createElement(_.Fragment,null,_.createElement("h2",null,"Unexpected Application Error!"),_.createElement("h3",{style:{fontStyle:"italic"}},a),r?_.createElement("pre",{style:o},r):null,d)}var g1=_.createElement(y1,null),v1=class extends _.Component{constructor(l){super(l),this.state={location:l.location,revalidation:l.revalidation,error:l.error}}static getDerivedStateFromError(l){return{error:l}}static getDerivedStateFromProps(l,a){return a.location!==l.location||a.revalidation!=="idle"&&l.revalidation==="idle"?{error:l.error,location:l.location,revalidation:l.revalidation}:{error:l.error!==void 0?l.error:a.error,location:a.location,revalidation:l.revalidation||a.revalidation}}componentDidCatch(l,a){console.error("React Router caught the following error during render",l,a)}render(){return this.state.error!==void 0?_.createElement(qn.Provider,{value:this.props.routeContext},_.createElement(Gf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function b1({routeContext:l,match:a,children:r}){let s=_.useContext(Ri);return s&&s.static&&s.staticContext&&(a.route.errorElement||a.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=a.route.id),_.createElement(qn.Provider,{value:l},r)}function S1(l,a=[],r=null,s=null){if(l==null){if(!r)return null;if(r.errors)l=r.matches;else if(a.length===0&&!r.initialized&&r.matches.length>0)l=r.matches;else return null}let o=l,f=r==null?void 0:r.errors;if(f!=null){let p=o.findIndex(m=>m.route.id&&(f==null?void 0:f[m.route.id])!==void 0);nt(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),o=o.slice(0,Math.min(o.length,p+1))}let d=!1,h=-1;if(r)for(let p=0;p<o.length;p++){let m=o[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(h=p),m.route.id){let{loaderData:g,errors:S}=r,x=m.route.loader&&!g.hasOwnProperty(m.route.id)&&(!S||S[m.route.id]===void 0);if(m.route.lazy||x){d=!0,h>=0?o=o.slice(0,h+1):o=[o[0]];break}}}return o.reduceRight((p,m,g)=>{let S,x=!1,R=null,E=null;r&&(S=f&&m.route.id?f[m.route.id]:void 0,R=m.route.errorElement||g1,d&&(h<0&&g===0?(cy("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),x=!0,E=null):h===g&&(x=!0,E=m.route.hydrateFallbackElement||null)));let C=a.concat(o.slice(0,g+1)),D=()=>{let w;return S?w=R:x?w=E:m.route.Component?w=_.createElement(m.route.Component,null):m.route.element?w=m.route.element:w=p,_.createElement(b1,{match:m,routeContext:{outlet:p,matches:C,isDataRoute:r!=null},children:w})};return r&&(m.route.ErrorBoundary||m.route.errorElement||g===0)?_.createElement(v1,{location:r.location,revalidation:r.revalidation,component:R,error:S,children:D(),routeContext:{outlet:null,matches:C,isDataRoute:!0}}):D()},null)}function $f(l){return`${l} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function x1(l){let a=_.useContext(Ri);return nt(a,$f(l)),a}function E1(l){let a=_.useContext(Nu);return nt(a,$f(l)),a}function w1(l){let a=_.useContext(qn);return nt(a,$f(l)),a}function Xf(l){let a=w1(l),r=a.matches[a.matches.length-1];return nt(r.route.id,`${l} can only be used on routes that contain a unique "id"`),r.route.id}function A1(){return Xf("useRouteId")}function _1(){var s;let l=_.useContext(Gf),a=E1("useRouteError"),r=Xf("useRouteError");return l!==void 0?l:(s=a.errors)==null?void 0:s[r]}function T1(){let{router:l}=x1("useNavigate"),a=Xf("useNavigate"),r=_.useRef(!1);return uy(()=>{r.current=!0}),_.useCallback(async(o,f={})=>{Ln(r.current,sy),r.current&&(typeof o=="number"?l.navigate(o):await l.navigate(o,{fromRouteId:a,...f}))},[l,a])}var i0={};function cy(l,a,r){!a&&!i0[l]&&(i0[l]=!0,Ln(!1,r))}_.memo(R1);function R1({routes:l,future:a,state:r}){return oy(l,void 0,r,a)}function uf({to:l,replace:a,state:r,relative:s}){nt(Oi(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=_.useContext(Hn);Ln(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=_.useContext(qn),{pathname:d}=sl(),h=Vn(),p=Yf(l,Ff(f),d,s==="path"),m=JSON.stringify(p);return _.useEffect(()=>{h(JSON.parse(m),{replace:a,state:r,relative:s})},[h,m,s,a,r]),null}function O1(l){return m1(l.context)}function In(l){nt(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function j1({basename:l="/",children:a=null,location:r,navigationType:s="POP",navigator:o,static:f=!1}){nt(!Oi(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=l.replace(/^\/*/,"/"),h=_.useMemo(()=>({basename:d,navigator:o,static:f,future:{}}),[d,o,f]);typeof r=="string"&&(r=Ti(r));let{pathname:p="/",search:m="",hash:g="",state:S=null,key:x="default"}=r,R=_.useMemo(()=>{let E=Ul(p,d);return E==null?null:{location:{pathname:E,search:m,hash:g,state:S,key:x},navigationType:s}},[d,p,m,g,S,x,s]);return Ln(R!=null,`<Router basename="${d}"> is not able to match the URL "${p}${m}${g}" because it does not start with the basename, so the <Router> won't render anything.`),R==null?null:_.createElement(Hn.Provider,{value:h},_.createElement(Fr.Provider,{children:a,value:R}))}function D1({children:l,location:a}){return p1(_f(l),a)}function _f(l,a=[]){let r=[];return _.Children.forEach(l,(s,o)=>{if(!_.isValidElement(s))return;let f=[...a,o];if(s.type===_.Fragment){r.push.apply(r,_f(s.props.children,f));return}nt(s.type===In,`[${typeof s.type=="string"?s.type:s.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),nt(!s.props.index||!s.props.children,"An index route cannot have child routes.");let d={id:s.props.id||f.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,hydrateFallbackElement:s.props.hydrateFallbackElement,HydrateFallback:s.props.HydrateFallback,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.hasErrorBoundary===!0||s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(d.children=_f(s.props.children,f)),r.push(d)}),r}var fu="get",du="application/x-www-form-urlencoded";function Mu(l){return l!=null&&typeof l.tagName=="string"}function C1(l){return Mu(l)&&l.tagName.toLowerCase()==="button"}function N1(l){return Mu(l)&&l.tagName.toLowerCase()==="form"}function M1(l){return Mu(l)&&l.tagName.toLowerCase()==="input"}function U1(l){return!!(l.metaKey||l.altKey||l.ctrlKey||l.shiftKey)}function z1(l,a){return l.button===0&&(!a||a==="_self")&&!U1(l)}var su=null;function L1(){if(su===null)try{new FormData(document.createElement("form"),0),su=!1}catch{su=!0}return su}var B1=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function of(l){return l!=null&&!B1.has(l)?(Ln(!1,`"${l}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${du}"`),null):l}function k1(l,a){let r,s,o,f,d;if(N1(l)){let h=l.getAttribute("action");s=h?Ul(h,a):null,r=l.getAttribute("method")||fu,o=of(l.getAttribute("enctype"))||du,f=new FormData(l)}else if(C1(l)||M1(l)&&(l.type==="submit"||l.type==="image")){let h=l.form;if(h==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=l.getAttribute("formaction")||h.getAttribute("action");if(s=p?Ul(p,a):null,r=l.getAttribute("formmethod")||h.getAttribute("method")||fu,o=of(l.getAttribute("formenctype"))||of(h.getAttribute("enctype"))||du,f=new FormData(h,l),!L1()){let{name:m,type:g,value:S}=l;if(g==="image"){let x=m?`${m}.`:"";f.append(`${x}x`,"0"),f.append(`${x}y`,"0")}else m&&f.append(m,S)}}else{if(Mu(l))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=fu,s=null,o=du,d=l}return f&&o==="text/plain"&&(d=f,f=void 0),{action:s,method:r.toLowerCase(),encType:o,formData:f,body:d}}function Zf(l,a){if(l===!1||l===null||typeof l>"u")throw new Error(a)}async function H1(l,a){if(l.id in a)return a[l.id];try{let r=await import(l.module);return a[l.id]=r,r}catch(r){return console.error(`Error loading route module \`${l.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function q1(l){return l==null?!1:l.href==null?l.rel==="preload"&&typeof l.imageSrcSet=="string"&&typeof l.imageSizes=="string":typeof l.rel=="string"&&typeof l.href=="string"}async function V1(l,a,r){let s=await Promise.all(l.map(async o=>{let f=a.routes[o.route.id];if(f){let d=await H1(f,r);return d.links?d.links():[]}return[]}));return $1(s.flat(1).filter(q1).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function r0(l,a,r,s,o,f){let d=(p,m)=>r[m]?p.route.id!==r[m].route.id:!0,h=(p,m)=>{var g;return r[m].pathname!==p.pathname||((g=r[m].route.path)==null?void 0:g.endsWith("*"))&&r[m].params["*"]!==p.params["*"]};return f==="assets"?a.filter((p,m)=>d(p,m)||h(p,m)):f==="data"?a.filter((p,m)=>{var S;let g=s.routes[p.route.id];if(!g||!g.hasLoader)return!1;if(d(p,m)||h(p,m))return!0;if(p.route.shouldRevalidate){let x=p.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:((S=r[0])==null?void 0:S.params)||{},nextUrl:new URL(l,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof x=="boolean")return x}return!0}):[]}function F1(l,a,{includeHydrateFallback:r}={}){return Y1(l.map(s=>{let o=a.routes[s.route.id];if(!o)return[];let f=[o.module];return o.clientActionModule&&(f=f.concat(o.clientActionModule)),o.clientLoaderModule&&(f=f.concat(o.clientLoaderModule)),r&&o.hydrateFallbackModule&&(f=f.concat(o.hydrateFallbackModule)),o.imports&&(f=f.concat(o.imports)),f}).flat(1))}function Y1(l){return[...new Set(l)]}function G1(l){let a={},r=Object.keys(l).sort();for(let s of r)a[s]=l[s];return a}function $1(l,a){let r=new Set;return new Set(a),l.reduce((s,o)=>{let f=JSON.stringify(G1(o));return r.has(f)||(r.add(f),s.push({key:f,link:o})),s},[])}var X1=new Set([100,101,204,205]);function Z1(l,a){let r=typeof l=="string"?new URL(l,typeof window>"u"?"server://singlefetch/":window.location.origin):l;return r.pathname==="/"?r.pathname="_root.data":a&&Ul(r.pathname,a)==="/"?r.pathname=`${a.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function fy(){let l=_.useContext(Ri);return Zf(l,"You must render this element inside a <DataRouterContext.Provider> element"),l}function Q1(){let l=_.useContext(Nu);return Zf(l,"You must render this element inside a <DataRouterStateContext.Provider> element"),l}var Qf=_.createContext(void 0);Qf.displayName="FrameworkContext";function dy(){let l=_.useContext(Qf);return Zf(l,"You must render this element inside a <HydratedRouter> element"),l}function K1(l,a){let r=_.useContext(Qf),[s,o]=_.useState(!1),[f,d]=_.useState(!1),{onFocus:h,onBlur:p,onMouseEnter:m,onMouseLeave:g,onTouchStart:S}=a,x=_.useRef(null);_.useEffect(()=>{if(l==="render"&&d(!0),l==="viewport"){let C=w=>{w.forEach(T=>{d(T.isIntersecting)})},D=new IntersectionObserver(C,{threshold:.5});return x.current&&D.observe(x.current),()=>{D.disconnect()}}},[l]),_.useEffect(()=>{if(s){let C=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(C)}}},[s]);let R=()=>{o(!0)},E=()=>{o(!1),d(!1)};return r?l!=="intent"?[f,x,{}]:[f,x,{onFocus:Cr(h,R),onBlur:Cr(p,E),onMouseEnter:Cr(m,R),onMouseLeave:Cr(g,E),onTouchStart:Cr(S,R)}]:[!1,x,{}]}function Cr(l,a){return r=>{l&&l(r),r.defaultPrevented||a(r)}}function J1({page:l,...a}){let{router:r}=fy(),s=_.useMemo(()=>ny(r.routes,l,r.basename),[r.routes,l,r.basename]);return s?_.createElement(W1,{page:l,matches:s,...a}):null}function P1(l){let{manifest:a,routeModules:r}=dy(),[s,o]=_.useState([]);return _.useEffect(()=>{let f=!1;return V1(l,a,r).then(d=>{f||o(d)}),()=>{f=!0}},[l,a,r]),s}function W1({page:l,matches:a,...r}){let s=sl(),{manifest:o,routeModules:f}=dy(),{basename:d}=fy(),{loaderData:h,matches:p}=Q1(),m=_.useMemo(()=>r0(l,a,p,o,s,"data"),[l,a,p,o,s]),g=_.useMemo(()=>r0(l,a,p,o,s,"assets"),[l,a,p,o,s]),S=_.useMemo(()=>{if(l===s.pathname+s.search+s.hash)return[];let E=new Set,C=!1;if(a.forEach(w=>{var M;let T=o.routes[w.route.id];!T||!T.hasLoader||(!m.some(X=>X.route.id===w.route.id)&&w.route.id in h&&((M=f[w.route.id])!=null&&M.shouldRevalidate)||T.hasClientLoader?C=!0:E.add(w.route.id))}),E.size===0)return[];let D=Z1(l,d);return C&&E.size>0&&D.searchParams.set("_routes",a.filter(w=>E.has(w.route.id)).map(w=>w.route.id).join(",")),[D.pathname+D.search]},[d,h,s,o,m,a,l,f]),x=_.useMemo(()=>F1(g,o),[g,o]),R=P1(g);return _.createElement(_.Fragment,null,S.map(E=>_.createElement("link",{key:E,rel:"prefetch",as:"fetch",href:E,...r})),x.map(E=>_.createElement("link",{key:E,rel:"modulepreload",href:E,...r})),R.map(({key:E,link:C})=>_.createElement("link",{key:E,...C})))}function I1(...l){return a=>{l.forEach(r=>{typeof r=="function"?r(a):r!=null&&(r.current=a)})}}var hy=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{hy&&(window.__reactRouterVersion="7.5.2")}catch{}function eS({basename:l,children:a,window:r}){let s=_.useRef();s.current==null&&(s.current=Hb({window:r,v5Compat:!0}));let o=s.current,[f,d]=_.useState({action:o.action,location:o.location}),h=_.useCallback(p=>{_.startTransition(()=>d(p))},[d]);return _.useLayoutEffect(()=>o.listen(h),[o,h]),_.createElement(j1,{basename:l,children:a,location:f.location,navigationType:f.action,navigator:o})}var my=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,py=_.forwardRef(function({onClick:a,discover:r="render",prefetch:s="none",relative:o,reloadDocument:f,replace:d,state:h,target:p,to:m,preventScrollReset:g,viewTransition:S,...x},R){let{basename:E}=_.useContext(Hn),C=typeof m=="string"&&my.test(m),D,w=!1;if(typeof m=="string"&&C&&(D=m,hy))try{let fe=new URL(window.location.href),De=m.startsWith("//")?new URL(fe.protocol+m):new URL(m),ge=Ul(De.pathname,E);De.origin===fe.origin&&ge!=null?m=ge+De.search+De.hash:w=!0}catch{Ln(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let T=f1(m,{relative:o}),[M,X,V]=K1(s,x),I=aS(m,{replace:d,state:h,target:p,preventScrollReset:g,relative:o,viewTransition:S});function W(fe){a&&a(fe),fe.defaultPrevented||I(fe)}let se=_.createElement("a",{...x,...V,href:D||T,onClick:w||f?a:W,ref:I1(R,X),target:p,"data-discover":!C&&r==="render"?"true":void 0});return M&&!C?_.createElement(_.Fragment,null,se,_.createElement(J1,{page:T})):se});py.displayName="Link";var tS=_.forwardRef(function({"aria-current":a="page",caseSensitive:r=!1,className:s="",end:o=!1,style:f,to:d,viewTransition:h,children:p,...m},g){let S=Yr(d,{relative:m.relative}),x=sl(),R=_.useContext(Nu),{navigator:E,basename:C}=_.useContext(Hn),D=R!=null&&oS(S)&&h===!0,w=E.encodeLocation?E.encodeLocation(S).pathname:S.pathname,T=x.pathname,M=R&&R.navigation&&R.navigation.location?R.navigation.location.pathname:null;r||(T=T.toLowerCase(),M=M?M.toLowerCase():null,w=w.toLowerCase()),M&&C&&(M=Ul(M,C)||M);const X=w!=="/"&&w.endsWith("/")?w.length-1:w.length;let V=T===w||!o&&T.startsWith(w)&&T.charAt(X)==="/",I=M!=null&&(M===w||!o&&M.startsWith(w)&&M.charAt(w.length)==="/"),W={isActive:V,isPending:I,isTransitioning:D},se=V?a:void 0,fe;typeof s=="function"?fe=s(W):fe=[s,V?"active":null,I?"pending":null,D?"transitioning":null].filter(Boolean).join(" ");let De=typeof f=="function"?f(W):f;return _.createElement(py,{...m,"aria-current":se,className:fe,ref:g,style:De,to:d,viewTransition:h},typeof p=="function"?p(W):p)});tS.displayName="NavLink";var nS=_.forwardRef(({discover:l="render",fetcherKey:a,navigate:r,reloadDocument:s,replace:o,state:f,method:d=fu,action:h,onSubmit:p,relative:m,preventScrollReset:g,viewTransition:S,...x},R)=>{let E=sS(),C=uS(h,{relative:m}),D=d.toLowerCase()==="get"?"get":"post",w=typeof h=="string"&&my.test(h),T=M=>{if(p&&p(M),M.defaultPrevented)return;M.preventDefault();let X=M.nativeEvent.submitter,V=(X==null?void 0:X.getAttribute("formmethod"))||d;E(X||M.currentTarget,{fetcherKey:a,method:V,navigate:r,replace:o,state:f,relative:m,preventScrollReset:g,viewTransition:S})};return _.createElement("form",{ref:R,method:D,action:C,onSubmit:s?p:T,...x,"data-discover":!w&&l==="render"?"true":void 0})});nS.displayName="Form";function lS(l){return`${l} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function yy(l){let a=_.useContext(Ri);return nt(a,lS(l)),a}function aS(l,{target:a,replace:r,state:s,preventScrollReset:o,relative:f,viewTransition:d}={}){let h=Vn(),p=sl(),m=Yr(l,{relative:f});return _.useCallback(g=>{if(z1(g,a)){g.preventDefault();let S=r!==void 0?r:kr(p)===kr(m);h(l,{replace:S,state:s,preventScrollReset:o,relative:f,viewTransition:d})}},[p,h,m,r,s,a,l,o,f,d])}var iS=0,rS=()=>`__${String(++iS)}__`;function sS(){let{router:l}=yy("useSubmit"),{basename:a}=_.useContext(Hn),r=A1();return _.useCallback(async(s,o={})=>{let{action:f,method:d,encType:h,formData:p,body:m}=k1(s,a);if(o.navigate===!1){let g=o.fetcherKey||rS();await l.fetch(g,r,o.action||f,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||d,formEncType:o.encType||h,flushSync:o.flushSync})}else await l.navigate(o.action||f,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||d,formEncType:o.encType||h,replace:o.replace,state:o.state,fromRouteId:r,flushSync:o.flushSync,viewTransition:o.viewTransition})},[l,a,r])}function uS(l,{relative:a}={}){let{basename:r}=_.useContext(Hn),s=_.useContext(qn);nt(s,"useFormAction must be used inside a RouteContext");let[o]=s.matches.slice(-1),f={...Yr(l||".",{relative:a})},d=sl();if(l==null){f.search=d.search;let h=new URLSearchParams(f.search),p=h.getAll("index");if(p.some(g=>g==="")){h.delete("index"),p.filter(S=>S).forEach(S=>h.append("index",S));let g=h.toString();f.search=g?`?${g}`:""}}return(!l||l===".")&&o.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(f.pathname=f.pathname==="/"?r:Nl([r,f.pathname])),kr(f)}function oS(l,a={}){let r=_.useContext(ry);nt(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:s}=yy("useViewTransitionState"),o=Yr(l,{relative:a.relative});if(!r.isTransitioning)return!1;let f=Ul(r.currentLocation.pathname,s)||r.currentLocation.pathname,d=Ul(r.nextLocation.pathname,s)||r.nextLocation.pathname;return bu(o.pathname,d)!=null||bu(o.pathname,f)!=null}new TextEncoder;[...X1];const cS="/assets/illustration1-BZ2qBqb5.png",fS="/assets/illustration2-D_JRMXuN.png",dS="/assets/illustration3-0vw7lghX.png",hS="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAhCAYAAAC803lsAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAANlSURBVHgBxVc9T9tAGH7vLqm6VErExGaIkBgDVaWO4RcUtm6EXwAZOwG/oHTrBv0FpL+AZENqoWFDype3jpgNNdjX57XPcDjBoc5HH+l057vz3eP324JGYHl5fR+dEwRBzXVbHs0BueREqbR+TCSqPFZKOeg2aA5Q9gNLQgixZ005xeLi7c3N73OaMUSCxMHjknax7GDg+b6/BhW5NEPkkiSEoLrWtKm1aGB8i6ldKdUp+jXKCMcpF5QSRzj9Az/j/JNe77Jm71Gl0ttNkPgakRA1rfUVRpt4bAWB/0lK+RGEVqEiARU1KAMWFhZxvtzGua+54Z73yfMkJj1D4rDT+XlkH8AeAyJbPETL5D0sjYgEke/nl/L5/JK5b9fel8PlDbJsJYl2+0cL3dKotdXVsjMYKHgZVSBJFz2L/PCZo/Ahd2aohhYlTYDBQJ4xCR7jCx22M9ib7XVk4lADDXai+iDeN0vfpkIEIq+wV8HwXN9/UxRCG+MT28m98Lot69FjM+h2L54QzlFG5PPkBUHoZbABrxAEksdo+ja5VynJxs/e4vZ6FyPVnFki7XarhYPZfkKR4ys/m8v6I7ZzymCSz9nPZDYiRHBlPbItsNirCAkP6kHKqMYq7HYvT2jaRNhjbLeEzovWFx/BhhwzHiuNiYgMBlEk5ijpuucujzudS45D7A1Ql/wFadwYabTSpJGZiC2NIMg/+VJ4CHsD1CQKUQsNugy3PqBpExkljQSYgMcqg83EZcR22pkvdl87isZzSWlYJAzu6P4+9CqMRWqKeLFE7CgaQ6k/leQ+Uy40yLg12ml4kRTfaVIiySiKqS/RSpilh4DEtkOPIdwzCfUg7Y6xqjG1RDU+lKOo1tIVHEZJF0a9c30d2k3VtBchlQgnMNy3b1s/i9vackJTwrOqwaXVKGwzCd3E1JbpGSzu2rjY8C9IlQjbBAjxhXUzVacJgADXx5nwtmAjWQOnGKtucqZE6J7o8qcQYd2Cqu/MSgHpRFA892nKgDdtcLiPyaysvCuPJTILsDehIOdIW2cyUNFZnKmHalWTto9pjkC9WxuSiBDSpTkD0qkI+g/gSI0y4Tj+k0TbmSsRjtIwUuv/Wjd9/1WVM3jm4jkLuFiKpRDln4uHH7q5Eol8QzfxL71nftwe8Bfkx1ujnHc2/QAAAABJRU5ErkJggg==";function mS(){const l=Vn();return v.jsxs("main",{className:"landing-page",children:[v.jsxs("section",{className:"left-panel",children:[v.jsxs("h1",{children:["BIZTREND",v.jsx("br",{}),"F",v.jsx("img",{src:hS,alt:"system icon"}),v.jsx("span",{children:"RECAST"})]}),v.jsx("h4",{children:"STAY AHEAD IN THE BUSINESS GAME!"}),v.jsx("br",{}),v.jsx("br",{}),v.jsx("p",{children:"Ready to explore?"}),v.jsx("p",{children:"Select your user type and we'll guide the way!"}),v.jsx("br",{}),v.jsxs("div",{className:"buttons-group extra-style",children:[v.jsx("button",{className:"admin-btn",onClick:()=>l("/login"),children:"Registered User"}),v.jsx("button",{className:"guest-btn",onClick:()=>l("/home"),children:"Guest"})]})]}),v.jsxs("section",{className:"right-panel",children:[v.jsx("div",{className:"big-circle"}),v.jsx("div",{className:"small-circle-top"}),v.jsx("div",{className:"small-circle-bottom"}),v.jsx("img",{className:"illustration-top",src:dS,alt:"illustration1"}),v.jsx("img",{className:"illustration-middle",src:fS,alt:"illustration2"}),v.jsx("img",{className:"illustration-bottom",src:cS,alt:"illustration3"})]})]})}const pS="/assets/login-DtlAx1r0.png",yS="/assets/Logo-CnD0Q2qt.png";var Gr=l=>l.type==="checkbox",Ra=l=>l instanceof Date,Yt=l=>l==null;const gy=l=>typeof l=="object";var dt=l=>!Yt(l)&&!Array.isArray(l)&&gy(l)&&!Ra(l),gS=l=>dt(l)&&l.target?Gr(l.target)?l.target.checked:l.target.value:l,vS=l=>l.substring(0,l.search(/\.\d+(\.|$)/))||l,bS=(l,a)=>l.has(vS(a)),SS=l=>{const a=l.constructor&&l.constructor.prototype;return dt(a)&&a.hasOwnProperty("isPrototypeOf")},Kf=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Bt(l){let a;const r=Array.isArray(l),s=typeof FileList<"u"?l instanceof FileList:!1;if(l instanceof Date)a=new Date(l);else if(l instanceof Set)a=new Set(l);else if(!(Kf&&(l instanceof Blob||s))&&(r||dt(l)))if(a=r?[]:{},!r&&!SS(l))a=l;else for(const o in l)l.hasOwnProperty(o)&&(a[o]=Bt(l[o]));else return l;return a}var Uu=l=>Array.isArray(l)?l.filter(Boolean):[],vt=l=>l===void 0,ce=(l,a,r)=>{if(!a||!dt(l))return r;const s=Uu(a.split(/[,[\].]+?/)).reduce((o,f)=>Yt(o)?o:o[f],l);return vt(s)||s===l?vt(l[a])?r:l[a]:s},el=l=>typeof l=="boolean",Jf=l=>/^\w*$/.test(l),vy=l=>Uu(l.replace(/["|']|\]/g,"").split(/\.|\[/)),Qe=(l,a,r)=>{let s=-1;const o=Jf(a)?[a]:vy(a),f=o.length,d=f-1;for(;++s<f;){const h=o[s];let p=r;if(s!==d){const m=l[h];p=dt(m)||Array.isArray(m)?m:isNaN(+o[s+1])?{}:[]}if(h==="__proto__"||h==="constructor"||h==="prototype")return;l[h]=p,l=l[h]}};const s0={BLUR:"blur",FOCUS_OUT:"focusout"},Un={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Dl={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};Je.createContext(null);var xS=(l,a,r,s=!0)=>{const o={defaultValues:a._defaultValues};for(const f in l)Object.defineProperty(o,f,{get:()=>{const d=f;return a._proxyFormState[d]!==Un.all&&(a._proxyFormState[d]=!s||Un.all),l[d]}});return o},Tf=l=>Yt(l)||!gy(l);function ra(l,a){if(Tf(l)||Tf(a))return l===a;if(Ra(l)&&Ra(a))return l.getTime()===a.getTime();const r=Object.keys(l),s=Object.keys(a);if(r.length!==s.length)return!1;for(const o of r){const f=l[o];if(!s.includes(o))return!1;if(o!=="ref"){const d=a[o];if(Ra(f)&&Ra(d)||dt(f)&&dt(d)||Array.isArray(f)&&Array.isArray(d)?!ra(f,d):f!==d)return!1}}return!0}var ll=l=>typeof l=="string",ES=(l,a,r,s,o)=>ll(l)?(s&&a.watch.add(l),ce(r,l,o)):Array.isArray(l)?l.map(f=>(s&&a.watch.add(f),ce(r,f))):(s&&(a.watchAll=!0),r),by=(l,a,r,s,o)=>a?{...r[l],types:{...r[l]&&r[l].types?r[l].types:{},[s]:o||!0}}:{},zr=l=>Array.isArray(l)?l:[l],u0=()=>{let l=[];return{get observers(){return l},next:o=>{for(const f of l)f.next&&f.next(o)},subscribe:o=>(l.push(o),{unsubscribe:()=>{l=l.filter(f=>f!==o)}}),unsubscribe:()=>{l=[]}}},Ft=l=>dt(l)&&!Object.keys(l).length,Pf=l=>l.type==="file",zn=l=>typeof l=="function",Su=l=>{if(!Kf)return!1;const a=l?l.ownerDocument:0;return l instanceof(a&&a.defaultView?a.defaultView.HTMLElement:HTMLElement)},Sy=l=>l.type==="select-multiple",Wf=l=>l.type==="radio",wS=l=>Wf(l)||Gr(l),cf=l=>Su(l)&&l.isConnected;function AS(l,a){const r=a.slice(0,-1).length;let s=0;for(;s<r;)l=vt(l)?s++:l[a[s++]];return l}function _S(l){for(const a in l)if(l.hasOwnProperty(a)&&!vt(l[a]))return!1;return!0}function Et(l,a){const r=Array.isArray(a)?a:Jf(a)?[a]:vy(a),s=r.length===1?l:AS(l,r),o=r.length-1,f=r[o];return s&&delete s[f],o!==0&&(dt(s)&&Ft(s)||Array.isArray(s)&&_S(s))&&Et(l,r.slice(0,-1)),l}var xy=l=>{for(const a in l)if(zn(l[a]))return!0;return!1};function xu(l,a={}){const r=Array.isArray(l);if(dt(l)||r)for(const s in l)Array.isArray(l[s])||dt(l[s])&&!xy(l[s])?(a[s]=Array.isArray(l[s])?[]:{},xu(l[s],a[s])):Yt(l[s])||(a[s]=!0);return a}function Ey(l,a,r){const s=Array.isArray(l);if(dt(l)||s)for(const o in l)Array.isArray(l[o])||dt(l[o])&&!xy(l[o])?vt(a)||Tf(r[o])?r[o]=Array.isArray(l[o])?xu(l[o],[]):{...xu(l[o])}:Ey(l[o],Yt(a)?{}:a[o],r[o]):r[o]=!ra(l[o],a[o]);return r}var Nr=(l,a)=>Ey(l,a,xu(a));const o0={value:!1,isValid:!1},c0={value:!0,isValid:!0};var wy=l=>{if(Array.isArray(l)){if(l.length>1){const a=l.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:a,isValid:!!a.length}}return l[0].checked&&!l[0].disabled?l[0].attributes&&!vt(l[0].attributes.value)?vt(l[0].value)||l[0].value===""?c0:{value:l[0].value,isValid:!0}:c0:o0}return o0},Ay=(l,{valueAsNumber:a,valueAsDate:r,setValueAs:s})=>vt(l)?l:a?l===""?NaN:l&&+l:r&&ll(l)?new Date(l):s?s(l):l;const f0={isValid:!1,value:null};var _y=l=>Array.isArray(l)?l.reduce((a,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:a,f0):f0;function d0(l){const a=l.ref;return Pf(a)?a.files:Wf(a)?_y(l.refs).value:Sy(a)?[...a.selectedOptions].map(({value:r})=>r):Gr(a)?wy(l.refs).value:Ay(vt(a.value)?l.ref.value:a.value,l)}var TS=(l,a,r,s)=>{const o={};for(const f of l){const d=ce(a,f);d&&Qe(o,f,d._f)}return{criteriaMode:r,names:[...l],fields:o,shouldUseNativeValidation:s}},Eu=l=>l instanceof RegExp,Mr=l=>vt(l)?l:Eu(l)?l.source:dt(l)?Eu(l.value)?l.value.source:l.value:l,h0=l=>({isOnSubmit:!l||l===Un.onSubmit,isOnBlur:l===Un.onBlur,isOnChange:l===Un.onChange,isOnAll:l===Un.all,isOnTouch:l===Un.onTouched});const m0="AsyncFunction";var RS=l=>!!l&&!!l.validate&&!!(zn(l.validate)&&l.validate.constructor.name===m0||dt(l.validate)&&Object.values(l.validate).find(a=>a.constructor.name===m0)),OS=l=>l.mount&&(l.required||l.min||l.max||l.maxLength||l.minLength||l.pattern||l.validate),p0=(l,a,r)=>!r&&(a.watchAll||a.watch.has(l)||[...a.watch].some(s=>l.startsWith(s)&&/^\.\w+/.test(l.slice(s.length))));const Lr=(l,a,r,s)=>{for(const o of r||Object.keys(l)){const f=ce(l,o);if(f){const{_f:d,...h}=f;if(d){if(d.refs&&d.refs[0]&&a(d.refs[0],o)&&!s)return!0;if(d.ref&&a(d.ref,d.name)&&!s)return!0;if(Lr(h,a))break}else if(dt(h)&&Lr(h,a))break}}};function y0(l,a,r){const s=ce(l,r);if(s||Jf(r))return{error:s,name:r};const o=r.split(".");for(;o.length;){const f=o.join("."),d=ce(a,f),h=ce(l,f);if(d&&!Array.isArray(d)&&r!==f)return{name:r};if(h&&h.type)return{name:f,error:h};o.pop()}return{name:r}}var jS=(l,a,r,s)=>{r(l);const{name:o,...f}=l;return Ft(f)||Object.keys(f).length>=Object.keys(a).length||Object.keys(f).find(d=>a[d]===(!s||Un.all))},DS=(l,a,r)=>!l||!a||l===a||zr(l).some(s=>s&&(r?s===a:s.startsWith(a)||a.startsWith(s))),CS=(l,a,r,s,o)=>o.isOnAll?!1:!r&&o.isOnTouch?!(a||l):(r?s.isOnBlur:o.isOnBlur)?!l:(r?s.isOnChange:o.isOnChange)?l:!0,NS=(l,a)=>!Uu(ce(l,a)).length&&Et(l,a),MS=(l,a,r)=>{const s=zr(ce(l,r));return Qe(s,"root",a[r]),Qe(l,r,s),l},hu=l=>ll(l);function g0(l,a,r="validate"){if(hu(l)||Array.isArray(l)&&l.every(hu)||el(l)&&!l)return{type:r,message:hu(l)?l:"",ref:a}}var vi=l=>dt(l)&&!Eu(l)?l:{value:l,message:""},v0=async(l,a,r,s,o,f)=>{const{ref:d,refs:h,required:p,maxLength:m,minLength:g,min:S,max:x,pattern:R,validate:E,name:C,valueAsNumber:D,mount:w}=l._f,T=ce(r,C);if(!w||a.has(C))return{};const M=h?h[0]:d,X=re=>{o&&M.reportValidity&&(M.setCustomValidity(el(re)?"":re||""),M.reportValidity())},V={},I=Wf(d),W=Gr(d),se=I||W,fe=(D||Pf(d))&&vt(d.value)&&vt(T)||Su(d)&&d.value===""||T===""||Array.isArray(T)&&!T.length,De=by.bind(null,C,s,V),ge=(re,ye,de,_e=Dl.maxLength,H=Dl.minLength)=>{const J=re?ye:de;V[C]={type:re?_e:H,message:J,ref:d,...De(re?_e:H,J)}};if(f?!Array.isArray(T)||!T.length:p&&(!se&&(fe||Yt(T))||el(T)&&!T||W&&!wy(h).isValid||I&&!_y(h).isValid)){const{value:re,message:ye}=hu(p)?{value:!!p,message:p}:vi(p);if(re&&(V[C]={type:Dl.required,message:ye,ref:M,...De(Dl.required,ye)},!s))return X(ye),V}if(!fe&&(!Yt(S)||!Yt(x))){let re,ye;const de=vi(x),_e=vi(S);if(!Yt(T)&&!isNaN(T)){const H=d.valueAsNumber||T&&+T;Yt(de.value)||(re=H>de.value),Yt(_e.value)||(ye=H<_e.value)}else{const H=d.valueAsDate||new Date(T),J=O=>new Date(new Date().toDateString()+" "+O),te=d.type=="time",we=d.type=="week";ll(de.value)&&T&&(re=te?J(T)>J(de.value):we?T>de.value:H>new Date(de.value)),ll(_e.value)&&T&&(ye=te?J(T)<J(_e.value):we?T<_e.value:H<new Date(_e.value))}if((re||ye)&&(ge(!!re,de.message,_e.message,Dl.max,Dl.min),!s))return X(V[C].message),V}if((m||g)&&!fe&&(ll(T)||f&&Array.isArray(T))){const re=vi(m),ye=vi(g),de=!Yt(re.value)&&T.length>+re.value,_e=!Yt(ye.value)&&T.length<+ye.value;if((de||_e)&&(ge(de,re.message,ye.message),!s))return X(V[C].message),V}if(R&&!fe&&ll(T)){const{value:re,message:ye}=vi(R);if(Eu(re)&&!T.match(re)&&(V[C]={type:Dl.pattern,message:ye,ref:d,...De(Dl.pattern,ye)},!s))return X(ye),V}if(E){if(zn(E)){const re=await E(T,r),ye=g0(re,M);if(ye&&(V[C]={...ye,...De(Dl.validate,ye.message)},!s))return X(ye.message),V}else if(dt(E)){let re={};for(const ye in E){if(!Ft(re)&&!s)break;const de=g0(await E[ye](T,r),M,ye);de&&(re={...de,...De(ye,de.message)},X(de.message),s&&(V[C]=re))}if(!Ft(re)&&(V[C]={ref:M,...re},!s))return V}}return X(!0),V};const US={mode:Un.onSubmit,reValidateMode:Un.onChange,shouldFocusError:!0};function zS(l={}){let a={...US,...l},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:zn(a.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1};const s={};let o=dt(a.defaultValues)||dt(a.values)?Bt(a.values||a.defaultValues)||{}:{},f=a.shouldUnregister?{}:Bt(o),d={action:!1,mount:!1,watch:!1},h={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},p,m=0;const g={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let S={...g};const x={array:u0(),state:u0()},R=h0(a.mode),E=h0(a.reValidateMode),C=a.criteriaMode===Un.all,D=j=>B=>{clearTimeout(m),m=setTimeout(j,B)},w=async j=>{if(!a.disabled&&(g.isValid||S.isValid||j)){const B=a.resolver?Ft((await fe()).errors):await ge(s,!0);B!==r.isValid&&x.state.next({isValid:B})}},T=(j,B)=>{!a.disabled&&(g.isValidating||g.validatingFields||S.isValidating||S.validatingFields)&&((j||Array.from(h.mount)).forEach(G=>{G&&(B?Qe(r.validatingFields,G,B):Et(r.validatingFields,G))}),x.state.next({validatingFields:r.validatingFields,isValidating:!Ft(r.validatingFields)}))},M=(j,B=[],G,ie,P=!0,K=!0)=>{if(ie&&G&&!a.disabled){if(d.action=!0,K&&Array.isArray(ce(s,j))){const ne=G(ce(s,j),ie.argA,ie.argB);P&&Qe(s,j,ne)}if(K&&Array.isArray(ce(r.errors,j))){const ne=G(ce(r.errors,j),ie.argA,ie.argB);P&&Qe(r.errors,j,ne),NS(r.errors,j)}if((g.touchedFields||S.touchedFields)&&K&&Array.isArray(ce(r.touchedFields,j))){const ne=G(ce(r.touchedFields,j),ie.argA,ie.argB);P&&Qe(r.touchedFields,j,ne)}(g.dirtyFields||S.dirtyFields)&&(r.dirtyFields=Nr(o,f)),x.state.next({name:j,isDirty:ye(j,B),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else Qe(f,j,B)},X=(j,B)=>{Qe(r.errors,j,B),x.state.next({errors:r.errors})},V=j=>{r.errors=j,x.state.next({errors:r.errors,isValid:!1})},I=(j,B,G,ie)=>{const P=ce(s,j);if(P){const K=ce(f,j,vt(G)?ce(o,j):G);vt(K)||ie&&ie.defaultChecked||B?Qe(f,j,B?K:d0(P._f)):H(j,K),d.mount&&w()}},W=(j,B,G,ie,P)=>{let K=!1,ne=!1;const ve={name:j};if(!a.disabled){if(!G||ie){(g.isDirty||S.isDirty)&&(ne=r.isDirty,r.isDirty=ve.isDirty=ye(),K=ne!==ve.isDirty);const Pe=ra(ce(o,j),B);ne=!!ce(r.dirtyFields,j),Pe?Et(r.dirtyFields,j):Qe(r.dirtyFields,j,!0),ve.dirtyFields=r.dirtyFields,K=K||(g.dirtyFields||S.dirtyFields)&&ne!==!Pe}if(G){const Pe=ce(r.touchedFields,j);Pe||(Qe(r.touchedFields,j,G),ve.touchedFields=r.touchedFields,K=K||(g.touchedFields||S.touchedFields)&&Pe!==G)}K&&P&&x.state.next(ve)}return K?ve:{}},se=(j,B,G,ie)=>{const P=ce(r.errors,j),K=(g.isValid||S.isValid)&&el(B)&&r.isValid!==B;if(a.delayError&&G?(p=D(()=>X(j,G)),p(a.delayError)):(clearTimeout(m),p=null,G?Qe(r.errors,j,G):Et(r.errors,j)),(G?!ra(P,G):P)||!Ft(ie)||K){const ne={...ie,...K&&el(B)?{isValid:B}:{},errors:r.errors,name:j};r={...r,...ne},x.state.next(ne)}},fe=async j=>{T(j,!0);const B=await a.resolver(f,a.context,TS(j||h.mount,s,a.criteriaMode,a.shouldUseNativeValidation));return T(j),B},De=async j=>{const{errors:B}=await fe(j);if(j)for(const G of j){const ie=ce(B,G);ie?Qe(r.errors,G,ie):Et(r.errors,G)}else r.errors=B;return B},ge=async(j,B,G={valid:!0})=>{for(const ie in j){const P=j[ie];if(P){const{_f:K,...ne}=P;if(K){const ve=h.array.has(K.name),Pe=P._f&&RS(P._f);Pe&&g.validatingFields&&T([ie],!0);const et=await v0(P,h.disabled,f,C,a.shouldUseNativeValidation&&!B,ve);if(Pe&&g.validatingFields&&T([ie]),et[K.name]&&(G.valid=!1,B))break;!B&&(ce(et,K.name)?ve?MS(r.errors,et,K.name):Qe(r.errors,K.name,et[K.name]):Et(r.errors,K.name))}!Ft(ne)&&await ge(ne,B,G)}}return G.valid},re=()=>{for(const j of h.unMount){const B=ce(s,j);B&&(B._f.refs?B._f.refs.every(G=>!cf(G)):!cf(B._f.ref))&&bt(j)}h.unMount=new Set},ye=(j,B)=>!a.disabled&&(j&&B&&Qe(f,j,B),!ra(ae(),o)),de=(j,B,G)=>ES(j,h,{...d.mount?f:vt(B)?o:ll(j)?{[j]:B}:B},G,B),_e=j=>Uu(ce(d.mount?f:o,j,a.shouldUnregister?ce(o,j,[]):[])),H=(j,B,G={})=>{const ie=ce(s,j);let P=B;if(ie){const K=ie._f;K&&(!K.disabled&&Qe(f,j,Ay(B,K)),P=Su(K.ref)&&Yt(B)?"":B,Sy(K.ref)?[...K.ref.options].forEach(ne=>ne.selected=P.includes(ne.value)):K.refs?Gr(K.ref)?K.refs.length>1?K.refs.forEach(ne=>(!ne.defaultChecked||!ne.disabled)&&(ne.checked=Array.isArray(P)?!!P.find(ve=>ve===ne.value):P===ne.value)):K.refs[0]&&(K.refs[0].checked=!!P):K.refs.forEach(ne=>ne.checked=ne.value===P):Pf(K.ref)?K.ref.value="":(K.ref.value=P,K.ref.type||x.state.next({name:j,values:Bt(f)})))}(G.shouldDirty||G.shouldTouch)&&W(j,P,G.shouldTouch,G.shouldDirty,!0),G.shouldValidate&&Y(j)},J=(j,B,G)=>{for(const ie in B){const P=B[ie],K=`${j}.${ie}`,ne=ce(s,K);(h.array.has(j)||dt(P)||ne&&!ne._f)&&!Ra(P)?J(K,P,G):H(K,P,G)}},te=(j,B,G={})=>{const ie=ce(s,j),P=h.array.has(j),K=Bt(B);Qe(f,j,K),P?(x.array.next({name:j,values:Bt(f)}),(g.isDirty||g.dirtyFields||S.isDirty||S.dirtyFields)&&G.shouldDirty&&x.state.next({name:j,dirtyFields:Nr(o,f),isDirty:ye(j,K)})):ie&&!ie._f&&!Yt(K)?J(j,K,G):H(j,K,G),p0(j,h)&&x.state.next({...r}),x.state.next({name:d.mount?j:void 0,values:Bt(f)})},we=async j=>{d.mount=!0;const B=j.target;let G=B.name,ie=!0;const P=ce(s,G),K=ne=>{ie=Number.isNaN(ne)||Ra(ne)&&isNaN(ne.getTime())||ra(ne,ce(f,G,ne))};if(P){let ne,ve;const Pe=B.type?d0(P._f):gS(j),et=j.type===s0.BLUR||j.type===s0.FOCUS_OUT,On=!OS(P._f)&&!a.resolver&&!ce(r.errors,G)&&!P._f.deps||CS(et,ce(r.touchedFields,G),r.isSubmitted,E,R),mt=p0(G,h,et);Qe(f,G,Pe),et?(P._f.onBlur&&P._f.onBlur(j),p&&p(0)):P._f.onChange&&P._f.onChange(j);const qe=W(G,Pe,et),$t=!Ft(qe)||mt;if(!et&&x.state.next({name:G,type:j.type,values:Bt(f)}),On)return(g.isValid||S.isValid)&&(a.mode==="onBlur"?et&&w():et||w()),$t&&x.state.next({name:G,...mt?{}:qe});if(!et&&mt&&x.state.next({...r}),a.resolver){const{errors:ln}=await fe([G]);if(K(Pe),ie){const Xt=y0(r.errors,s,G),Gn=y0(ln,s,Xt.name||G);ne=Gn.error,G=Gn.name,ve=Ft(ln)}}else T([G],!0),ne=(await v0(P,h.disabled,f,C,a.shouldUseNativeValidation))[G],T([G]),K(Pe),ie&&(ne?ve=!1:(g.isValid||S.isValid)&&(ve=await ge(s,!0)));ie&&(P._f.deps&&Y(P._f.deps),se(G,ve,ne,qe))}},O=(j,B)=>{if(ce(r.errors,B)&&j.focus)return j.focus(),1},Y=async(j,B={})=>{let G,ie;const P=zr(j);if(a.resolver){const K=await De(vt(j)?j:P);G=Ft(K),ie=j?!P.some(ne=>ce(K,ne)):G}else j?(ie=(await Promise.all(P.map(async K=>{const ne=ce(s,K);return await ge(ne&&ne._f?{[K]:ne}:ne)}))).every(Boolean),!(!ie&&!r.isValid)&&w()):ie=G=await ge(s);return x.state.next({...!ll(j)||(g.isValid||S.isValid)&&G!==r.isValid?{}:{name:j},...a.resolver||!j?{isValid:G}:{},errors:r.errors}),B.shouldFocus&&!ie&&Lr(s,O,j?P:h.mount),ie},ae=j=>{const B={...d.mount?f:o};return vt(j)?B:ll(j)?ce(B,j):j.map(G=>ce(B,G))},ee=(j,B)=>({invalid:!!ce((B||r).errors,j),isDirty:!!ce((B||r).dirtyFields,j),error:ce((B||r).errors,j),isValidating:!!ce(r.validatingFields,j),isTouched:!!ce((B||r).touchedFields,j)}),he=j=>{j&&zr(j).forEach(B=>Et(r.errors,B)),x.state.next({errors:j?r.errors:{}})},je=(j,B,G)=>{const ie=(ce(s,j,{_f:{}})._f||{}).ref,P=ce(r.errors,j)||{},{ref:K,message:ne,type:ve,...Pe}=P;Qe(r.errors,j,{...Pe,...B,ref:ie}),x.state.next({name:j,errors:r.errors,isValid:!1}),G&&G.shouldFocus&&ie&&ie.focus&&ie.focus()},xe=(j,B)=>zn(j)?x.state.subscribe({next:G=>j(de(void 0,B),G)}):de(j,B,!0),at=j=>x.state.subscribe({next:B=>{DS(j.name,B.name,j.exact)&&jS(B,j.formState||g,Ye,j.reRenderRoot)&&j.callback({values:{...f},...r,...B})}}).unsubscribe,Te=j=>(d.mount=!0,S={...S,...j.formState},at({...j,formState:S})),bt=(j,B={})=>{for(const G of j?zr(j):h.mount)h.mount.delete(G),h.array.delete(G),B.keepValue||(Et(s,G),Et(f,G)),!B.keepError&&Et(r.errors,G),!B.keepDirty&&Et(r.dirtyFields,G),!B.keepTouched&&Et(r.touchedFields,G),!B.keepIsValidating&&Et(r.validatingFields,G),!a.shouldUnregister&&!B.keepDefaultValue&&Et(o,G);x.state.next({values:Bt(f)}),x.state.next({...r,...B.keepDirty?{isDirty:ye()}:{}}),!B.keepIsValid&&w()},wt=({disabled:j,name:B})=>{(el(j)&&d.mount||j||h.disabled.has(B))&&(j?h.disabled.add(B):h.disabled.delete(B))},jt=(j,B={})=>{let G=ce(s,j);const ie=el(B.disabled)||el(a.disabled);return Qe(s,j,{...G||{},_f:{...G&&G._f?G._f:{ref:{name:j}},name:j,mount:!0,...B}}),h.mount.add(j),G?wt({disabled:el(B.disabled)?B.disabled:a.disabled,name:j}):I(j,!0,B.value),{...ie?{disabled:B.disabled||a.disabled}:{},...a.progressive?{required:!!B.required,min:Mr(B.min),max:Mr(B.max),minLength:Mr(B.minLength),maxLength:Mr(B.maxLength),pattern:Mr(B.pattern)}:{},name:j,onChange:we,onBlur:we,ref:P=>{if(P){jt(j,B),G=ce(s,j);const K=vt(P.value)&&P.querySelectorAll&&P.querySelectorAll("input,select,textarea")[0]||P,ne=wS(K),ve=G._f.refs||[];if(ne?ve.find(Pe=>Pe===K):K===G._f.ref)return;Qe(s,j,{_f:{...G._f,...ne?{refs:[...ve.filter(cf),K,...Array.isArray(ce(o,j))?[{}]:[]],ref:{type:K.type,name:j}}:{ref:K}}}),I(j,!1,void 0,K)}else G=ce(s,j,{}),G._f&&(G._f.mount=!1),(a.shouldUnregister||B.shouldUnregister)&&!(bS(h.array,j)&&d.action)&&h.unMount.add(j)}}},mn=()=>a.shouldFocusError&&Lr(s,O,h.mount),Gt=j=>{el(j)&&(x.state.next({disabled:j}),Lr(s,(B,G)=>{const ie=ce(s,G);ie&&(B.disabled=ie._f.disabled||j,Array.isArray(ie._f.refs)&&ie._f.refs.forEach(P=>{P.disabled=ie._f.disabled||j}))},0,!1))},Yn=(j,B)=>async G=>{let ie;G&&(G.preventDefault&&G.preventDefault(),G.persist&&G.persist());let P=Bt(f);if(x.state.next({isSubmitting:!0}),a.resolver){const{errors:K,values:ne}=await fe();r.errors=K,P=ne}else await ge(s);if(h.disabled.size)for(const K of h.disabled)Qe(P,K,void 0);if(Et(r.errors,"root"),Ft(r.errors)){x.state.next({errors:{}});try{await j(P,G)}catch(K){ie=K}}else B&&await B({...r.errors},G),mn(),setTimeout(mn);if(x.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Ft(r.errors)&&!ie,submitCount:r.submitCount+1,errors:r.errors}),ie)throw ie},Dt=(j,B={})=>{ce(s,j)&&(vt(B.defaultValue)?te(j,Bt(ce(o,j))):(te(j,B.defaultValue),Qe(o,j,Bt(B.defaultValue))),B.keepTouched||Et(r.touchedFields,j),B.keepDirty||(Et(r.dirtyFields,j),r.isDirty=B.defaultValue?ye(j,Bt(ce(o,j))):ye()),B.keepError||(Et(r.errors,j),g.isValid&&w()),x.state.next({...r}))},ol=(j,B={})=>{const G=j?Bt(j):o,ie=Bt(G),P=Ft(j),K=P?o:ie;if(B.keepDefaultValues||(o=G),!B.keepValues){if(B.keepDirtyValues){const ne=new Set([...h.mount,...Object.keys(Nr(o,f))]);for(const ve of Array.from(ne))ce(r.dirtyFields,ve)?Qe(K,ve,ce(f,ve)):te(ve,ce(K,ve))}else{if(Kf&&vt(j))for(const ne of h.mount){const ve=ce(s,ne);if(ve&&ve._f){const Pe=Array.isArray(ve._f.refs)?ve._f.refs[0]:ve._f.ref;if(Su(Pe)){const et=Pe.closest("form");if(et){et.reset();break}}}}for(const ne of h.mount)te(ne,ce(K,ne))}f=Bt(K),x.array.next({values:{...K}}),x.state.next({values:{...K}})}h={mount:B.keepDirtyValues?h.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!g.isValid||!!B.keepIsValid||!!B.keepDirtyValues,d.watch=!!a.shouldUnregister,x.state.next({submitCount:B.keepSubmitCount?r.submitCount:0,isDirty:P?!1:B.keepDirty?r.isDirty:!!(B.keepDefaultValues&&!ra(j,o)),isSubmitted:B.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:P?{}:B.keepDirtyValues?B.keepDefaultValues&&f?Nr(o,f):r.dirtyFields:B.keepDefaultValues&&j?Nr(o,j):B.keepDirty?r.dirtyFields:{},touchedFields:B.keepTouched?r.touchedFields:{},errors:B.keepErrors?r.errors:{},isSubmitSuccessful:B.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1})},st=(j,B)=>ol(zn(j)?j(f):j,B),Rn=(j,B={})=>{const G=ce(s,j),ie=G&&G._f;if(ie){const P=ie.refs?ie.refs[0]:ie.ref;P.focus&&(P.focus(),B.shouldSelect&&zn(P.select)&&P.select())}},Ye=j=>{r={...r,...j}},nn={control:{register:jt,unregister:bt,getFieldState:ee,handleSubmit:Yn,setError:je,_subscribe:at,_runSchema:fe,_getWatch:de,_getDirty:ye,_setValid:w,_setFieldArray:M,_setDisabledField:wt,_setErrors:V,_getFieldArray:_e,_reset:ol,_resetDefaultValues:()=>zn(a.defaultValues)&&a.defaultValues().then(j=>{st(j,a.resetOptions),x.state.next({isLoading:!1})}),_removeUnmounted:re,_disableForm:Gt,_subjects:x,_proxyFormState:g,get _fields(){return s},get _formValues(){return f},get _state(){return d},set _state(j){d=j},get _defaultValues(){return o},get _names(){return h},set _names(j){h=j},get _formState(){return r},get _options(){return a},set _options(j){a={...a,...j}}},subscribe:Te,trigger:Y,register:jt,handleSubmit:Yn,watch:xe,setValue:te,getValues:ae,reset:st,resetField:Dt,clearErrors:he,unregister:bt,setError:je,setFocus:Rn,getFieldState:ee};return{...nn,formControl:nn}}const LS=typeof window<"u"?Je.useLayoutEffect:Je.useEffect;function $r(l={}){const a=Je.useRef(void 0),r=Je.useRef(void 0),[s,o]=Je.useState({isDirty:!1,isValidating:!1,isLoading:zn(l.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:l.errors||{},disabled:l.disabled||!1,isReady:!1,defaultValues:zn(l.defaultValues)?void 0:l.defaultValues});a.current||(a.current={...l.formControl?l.formControl:zS(l),formState:s},l.formControl&&l.defaultValues&&!zn(l.defaultValues)&&l.formControl.reset(l.defaultValues,l.resetOptions));const f=a.current.control;return f._options=l,LS(()=>{const d=f._subscribe({formState:f._proxyFormState,callback:()=>o({...f._formState}),reRenderRoot:!0});return o(h=>({...h,isReady:!0})),f._formState.isReady=!0,d},[f]),Je.useEffect(()=>f._disableForm(l.disabled),[f,l.disabled]),Je.useEffect(()=>{l.mode&&(f._options.mode=l.mode),l.reValidateMode&&(f._options.reValidateMode=l.reValidateMode),l.errors&&!Ft(l.errors)&&f._setErrors(l.errors)},[f,l.errors,l.mode,l.reValidateMode]),Je.useEffect(()=>{l.shouldUnregister&&f._subjects.state.next({values:f._getWatch()})},[f,l.shouldUnregister]),Je.useEffect(()=>{if(f._proxyFormState.isDirty){const d=f._getDirty();d!==s.isDirty&&f._subjects.state.next({isDirty:d})}},[f,s.isDirty]),Je.useEffect(()=>{l.values&&!ra(l.values,r.current)?(f._reset(l.values,f._options.resetOptions),r.current=l.values,o(d=>({...d}))):f._resetDefaultValues()},[f,l.values]),Je.useEffect(()=>{f._state.mount||(f._setValid(),f._state.mount=!0),f._state.watch&&(f._state.watch=!1,f._subjects.state.next({...f._formState})),f._removeUnmounted()}),a.current.formState=xS(s,f),a.current}var ff,b0;function BS(){if(b0)return ff;b0=1;function l(w){this._maxSize=w,this.clear()}l.prototype.clear=function(){this._size=0,this._values=Object.create(null)},l.prototype.get=function(w){return this._values[w]},l.prototype.set=function(w,T){return this._size>=this._maxSize&&this.clear(),w in this._values||this._size++,this._values[w]=T};var a=/[^.^\]^[]+|(?=\[\]|\.\.)/g,r=/^\d+$/,s=/^\d/,o=/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,f=/^\s*(['"]?)(.*?)(\1)\s*$/,d=512,h=new l(d),p=new l(d),m=new l(d);ff={Cache:l,split:S,normalizePath:g,setter:function(w){var T=g(w);return p.get(w)||p.set(w,function(X,V){for(var I=0,W=T.length,se=X;I<W-1;){var fe=T[I];if(fe==="__proto__"||fe==="constructor"||fe==="prototype")return X;se=se[T[I++]]}se[T[I]]=V})},getter:function(w,T){var M=g(w);return m.get(w)||m.set(w,function(V){for(var I=0,W=M.length;I<W;)if(V!=null||!T)V=V[M[I++]];else return;return V})},join:function(w){return w.reduce(function(T,M){return T+(R(M)||r.test(M)?"["+M+"]":(T?".":"")+M)},"")},forEach:function(w,T,M){x(Array.isArray(w)?w:S(w),T,M)}};function g(w){return h.get(w)||h.set(w,S(w).map(function(T){return T.replace(f,"$2")}))}function S(w){return w.match(a)||[""]}function x(w,T,M){var X=w.length,V,I,W,se;for(I=0;I<X;I++)V=w[I],V&&(D(V)&&(V='"'+V+'"'),se=R(V),W=!se&&/^\d+$/.test(V),T.call(M,V,se,W,I,w))}function R(w){return typeof w=="string"&&w&&["'",'"'].indexOf(w.charAt(0))!==-1}function E(w){return w.match(s)&&!w.match(r)}function C(w){return o.test(w)}function D(w){return!R(w)&&(E(w)||C(w))}return ff}var Da=BS(),df,S0;function kS(){if(S0)return df;S0=1;const l=/[A-Z\xc0-\xd6\xd8-\xde]?[a-z\xdf-\xf6\xf8-\xff]+(?:['’](?:d|ll|m|re|s|t|ve))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde]|$)|(?:[A-Z\xc0-\xd6\xd8-\xde]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:D|LL|M|RE|S|T|VE))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde](?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])|$)|[A-Z\xc0-\xd6\xd8-\xde]?(?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:d|ll|m|re|s|t|ve))?|[A-Z\xc0-\xd6\xd8-\xde]+(?:['’](?:D|LL|M|RE|S|T|VE))?|\d*(?:1ST|2ND|3RD|(?![123])\dTH)(?=\b|[a-z_])|\d*(?:1st|2nd|3rd|(?![123])\dth)(?=\b|[A-Z_])|\d+|(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g,a=g=>g.match(l)||[],r=g=>g[0].toUpperCase()+g.slice(1),s=(g,S)=>a(g).join(S).toLowerCase(),o=g=>a(g).reduce((S,x)=>`${S}${S?x[0].toUpperCase()+x.slice(1).toLowerCase():x.toLowerCase()}`,"");return df={words:a,upperFirst:r,camelCase:o,pascalCase:g=>r(o(g)),snakeCase:g=>s(g,"_"),kebabCase:g=>s(g,"-"),sentenceCase:g=>r(s(g," ")),titleCase:g=>a(g).map(r).join(" ")},df}var hf=kS(),uu={exports:{}},x0;function HS(){if(x0)return uu.exports;x0=1,uu.exports=function(o){return l(a(o),o)},uu.exports.array=l;function l(o,f){var d=o.length,h=new Array(d),p={},m=d,g=r(f),S=s(o);for(f.forEach(function(R){if(!S.has(R[0])||!S.has(R[1]))throw new Error("Unknown node. There is an unknown node in the supplied edges.")});m--;)p[m]||x(o[m],m,new Set);return h;function x(R,E,C){if(C.has(R)){var D;try{D=", node was:"+JSON.stringify(R)}catch{D=""}throw new Error("Cyclic dependency"+D)}if(!S.has(R))throw new Error("Found unknown node. Make sure to provided all involved nodes. Unknown node: "+JSON.stringify(R));if(!p[E]){p[E]=!0;var w=g.get(R)||new Set;if(w=Array.from(w),E=w.length){C.add(R);do{var T=w[--E];x(T,S.get(T),C)}while(E);C.delete(R)}h[--d]=R}}}function a(o){for(var f=new Set,d=0,h=o.length;d<h;d++){var p=o[d];f.add(p[0]),f.add(p[1])}return Array.from(f)}function r(o){for(var f=new Map,d=0,h=o.length;d<h;d++){var p=o[d];f.has(p[0])||f.set(p[0],new Set),f.has(p[1])||f.set(p[1],new Set),f.get(p[0]).add(p[1])}return f}function s(o){for(var f=new Map,d=0,h=o.length;d<h;d++)f.set(o[d],d);return f}return uu.exports}var qS=HS();const VS=qf(qS),FS=Object.prototype.toString,YS=Error.prototype.toString,GS=RegExp.prototype.toString,$S=typeof Symbol<"u"?Symbol.prototype.toString:()=>"",XS=/^Symbol\((.*)\)(.*)$/;function ZS(l){return l!=+l?"NaN":l===0&&1/l<0?"-0":""+l}function E0(l,a=!1){if(l==null||l===!0||l===!1)return""+l;const r=typeof l;if(r==="number")return ZS(l);if(r==="string")return a?`"${l}"`:l;if(r==="function")return"[Function "+(l.name||"anonymous")+"]";if(r==="symbol")return $S.call(l).replace(XS,"Symbol($1)");const s=FS.call(l).slice(8,-1);return s==="Date"?isNaN(l.getTime())?""+l:l.toISOString(l):s==="Error"||l instanceof Error?"["+YS.call(l)+"]":s==="RegExp"?GS.call(l):null}function sa(l,a){let r=E0(l,a);return r!==null?r:JSON.stringify(l,function(s,o){let f=E0(this[s],a);return f!==null?f:o},2)}function Ty(l){return l==null?[]:[].concat(l)}let Ry,Oy,jy,QS=/\$\{\s*(\w+)\s*\}/g;Ry=Symbol.toStringTag;class w0{constructor(a,r,s,o){this.name=void 0,this.message=void 0,this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=void 0,this.inner=void 0,this[Ry]="Error",this.name="ValidationError",this.value=r,this.path=s,this.type=o,this.errors=[],this.inner=[],Ty(a).forEach(f=>{if(Wt.isError(f)){this.errors.push(...f.errors);const d=f.inner.length?f.inner:[f];this.inner.push(...d)}else this.errors.push(f)}),this.message=this.errors.length>1?`${this.errors.length} errors occurred`:this.errors[0]}}Oy=Symbol.hasInstance;jy=Symbol.toStringTag;class Wt extends Error{static formatError(a,r){const s=r.label||r.path||"this";return r=Object.assign({},r,{path:s,originalPath:r.path}),typeof a=="string"?a.replace(QS,(o,f)=>sa(r[f])):typeof a=="function"?a(r):a}static isError(a){return a&&a.name==="ValidationError"}constructor(a,r,s,o,f){const d=new w0(a,r,s,o);if(f)return d;super(),this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=[],this.inner=[],this[jy]="Error",this.name=d.name,this.message=d.message,this.type=d.type,this.value=d.value,this.path=d.path,this.errors=d.errors,this.inner=d.inner,Error.captureStackTrace&&Error.captureStackTrace(this,Wt)}static[Oy](a){return w0[Symbol.hasInstance](a)||super[Symbol.hasInstance](a)}}let tl={default:"${path} is invalid",required:"${path} is a required field",defined:"${path} must be defined",notNull:"${path} cannot be null",oneOf:"${path} must be one of the following values: ${values}",notOneOf:"${path} must not be one of the following values: ${values}",notType:({path:l,type:a,value:r,originalValue:s})=>{const o=s!=null&&s!==r?` (cast from the value \`${sa(s,!0)}\`).`:".";return a!=="mixed"?`${l} must be a \`${a}\` type, but the final value was: \`${sa(r,!0)}\``+o:`${l} must match the configured type. The validated value was: \`${sa(r,!0)}\``+o}},Pt={length:"${path} must be exactly ${length} characters",min:"${path} must be at least ${min} characters",max:"${path} must be at most ${max} characters",matches:'${path} must match the following: "${regex}"',email:"${path} must be a valid email",url:"${path} must be a valid URL",uuid:"${path} must be a valid UUID",datetime:"${path} must be a valid ISO date-time",datetime_precision:"${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits",datetime_offset:'${path} must be a valid ISO date-time with UTC "Z" timezone',trim:"${path} must be a trimmed string",lowercase:"${path} must be a lowercase string",uppercase:"${path} must be a upper case string"},KS={min:"${path} must be greater than or equal to ${min}",max:"${path} must be less than or equal to ${max}",lessThan:"${path} must be less than ${less}",moreThan:"${path} must be greater than ${more}",positive:"${path} must be a positive number",negative:"${path} must be a negative number",integer:"${path} must be an integer"},Rf={min:"${path} field must be later than ${min}",max:"${path} field must be at earlier than ${max}"},JS={isValue:"${path} field must be ${value}"},mu={noUnknown:"${path} field has unspecified keys: ${unknown}",exact:"${path} object contains unknown properties: ${properties}"},PS={min:"${path} field must have at least ${min} items",max:"${path} field must have less than or equal to ${max} items",length:"${path} must have ${length} items"},WS={notType:l=>{const{path:a,value:r,spec:s}=l,o=s.types.length;if(Array.isArray(r)){if(r.length<o)return`${a} tuple value has too few items, expected a length of ${o} but got ${r.length} for value: \`${sa(r,!0)}\``;if(r.length>o)return`${a} tuple value has too many items, expected a length of ${o} but got ${r.length} for value: \`${sa(r,!0)}\``}return Wt.formatError(tl.notType,l)}};Object.assign(Object.create(null),{mixed:tl,string:Pt,number:KS,date:Rf,object:mu,array:PS,boolean:JS,tuple:WS});const If=l=>l&&l.__isYupSchema__;class wu{static fromOptions(a,r){if(!r.then&&!r.otherwise)throw new TypeError("either `then:` or `otherwise:` is required for `when()` conditions");let{is:s,then:o,otherwise:f}=r,d=typeof s=="function"?s:(...h)=>h.every(p=>p===s);return new wu(a,(h,p)=>{var m;let g=d(...h)?o:f;return(m=g==null?void 0:g(p))!=null?m:p})}constructor(a,r){this.fn=void 0,this.refs=a,this.refs=a,this.fn=r}resolve(a,r){let s=this.refs.map(f=>f.getValue(r==null?void 0:r.value,r==null?void 0:r.parent,r==null?void 0:r.context)),o=this.fn(s,a,r);if(o===void 0||o===a)return a;if(!If(o))throw new TypeError("conditions must return a schema object");return o.resolve(r)}}const ou={context:"$",value:"."};function ed(l,a){return new oa(l,a)}class oa{constructor(a,r={}){if(this.key=void 0,this.isContext=void 0,this.isValue=void 0,this.isSibling=void 0,this.path=void 0,this.getter=void 0,this.map=void 0,typeof a!="string")throw new TypeError("ref must be a string, got: "+a);if(this.key=a.trim(),a==="")throw new TypeError("ref must be a non-empty string");this.isContext=this.key[0]===ou.context,this.isValue=this.key[0]===ou.value,this.isSibling=!this.isContext&&!this.isValue;let s=this.isContext?ou.context:this.isValue?ou.value:"";this.path=this.key.slice(s.length),this.getter=this.path&&Da.getter(this.path,!0),this.map=r.map}getValue(a,r,s){let o=this.isContext?s:this.isValue?a:r;return this.getter&&(o=this.getter(o||{})),this.map&&(o=this.map(o)),o}cast(a,r){return this.getValue(a,r==null?void 0:r.parent,r==null?void 0:r.context)}resolve(){return this}describe(){return{type:"ref",key:this.key}}toString(){return`Ref(${this.key})`}static isRef(a){return a&&a.__isYupRef}}oa.prototype.__isYupRef=!0;const Oa=l=>l==null;function bi(l){function a({value:r,path:s="",options:o,originalValue:f,schema:d},h,p){const{name:m,test:g,params:S,message:x,skipAbsent:R}=l;let{parent:E,context:C,abortEarly:D=d.spec.abortEarly,disableStackTrace:w=d.spec.disableStackTrace}=o;function T(ge){return oa.isRef(ge)?ge.getValue(r,E,C):ge}function M(ge={}){const re=Object.assign({value:r,originalValue:f,label:d.spec.label,path:ge.path||s,spec:d.spec,disableStackTrace:ge.disableStackTrace||w},S,ge.params);for(const de of Object.keys(re))re[de]=T(re[de]);const ye=new Wt(Wt.formatError(ge.message||x,re),r,re.path,ge.type||m,re.disableStackTrace);return ye.params=re,ye}const X=D?h:p;let V={path:s,parent:E,type:m,from:o.from,createError:M,resolve:T,options:o,originalValue:f,schema:d};const I=ge=>{Wt.isError(ge)?X(ge):ge?p(null):X(M())},W=ge=>{Wt.isError(ge)?X(ge):h(ge)};if(R&&Oa(r))return I(!0);let fe;try{var De;if(fe=g.call(V,r,V),typeof((De=fe)==null?void 0:De.then)=="function"){if(o.sync)throw new Error(`Validation test of type: "${V.type}" returned a Promise during a synchronous validate. This test will finish after the validate call has returned`);return Promise.resolve(fe).then(I,W)}}catch(ge){W(ge);return}I(fe)}return a.OPTIONS=l,a}function IS(l,a,r,s=r){let o,f,d;return a?(Da.forEach(a,(h,p,m)=>{let g=p?h.slice(1,h.length-1):h;l=l.resolve({context:s,parent:o,value:r});let S=l.type==="tuple",x=m?parseInt(g,10):0;if(l.innerType||S){if(S&&!m)throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part "${d}" must contain an index to the tuple element, e.g. "${d}[0]"`);if(r&&x>=r.length)throw new Error(`Yup.reach cannot resolve an array item at index: ${h}, in the path: ${a}. because there is no value at that index. `);o=r,r=r&&r[x],l=S?l.spec.types[x]:l.innerType}if(!m){if(!l.fields||!l.fields[g])throw new Error(`The schema does not contain the path: ${a}. (failed at: ${d} which is a type: "${l.type}")`);o=r,r=r&&r[g],l=l.fields[g]}f=g,d=p?"["+h+"]":"."+h}),{schema:l,parent:o,parentPath:f}):{parent:o,parentPath:a,schema:l}}class Au extends Set{describe(){const a=[];for(const r of this.values())a.push(oa.isRef(r)?r.describe():r);return a}resolveAll(a){let r=[];for(const s of this.values())r.push(a(s));return r}clone(){return new Au(this.values())}merge(a,r){const s=this.clone();return a.forEach(o=>s.add(o)),r.forEach(o=>s.delete(o)),s}}function xi(l,a=new Map){if(If(l)||!l||typeof l!="object")return l;if(a.has(l))return a.get(l);let r;if(l instanceof Date)r=new Date(l.getTime()),a.set(l,r);else if(l instanceof RegExp)r=new RegExp(l),a.set(l,r);else if(Array.isArray(l)){r=new Array(l.length),a.set(l,r);for(let s=0;s<l.length;s++)r[s]=xi(l[s],a)}else if(l instanceof Map){r=new Map,a.set(l,r);for(const[s,o]of l.entries())r.set(s,xi(o,a))}else if(l instanceof Set){r=new Set,a.set(l,r);for(const s of l)r.add(xi(s,a))}else if(l instanceof Object){r={},a.set(l,r);for(const[s,o]of Object.entries(l))r[s]=xi(o,a)}else throw Error(`Unable to clone ${l}`);return r}class il{constructor(a){this.type=void 0,this.deps=[],this.tests=void 0,this.transforms=void 0,this.conditions=[],this._mutate=void 0,this.internalTests={},this._whitelist=new Au,this._blacklist=new Au,this.exclusiveTests=Object.create(null),this._typeCheck=void 0,this.spec=void 0,this.tests=[],this.transforms=[],this.withMutation(()=>{this.typeError(tl.notType)}),this.type=a.type,this._typeCheck=a.check,this.spec=Object.assign({strip:!1,strict:!1,abortEarly:!0,recursive:!0,disableStackTrace:!1,nullable:!1,optional:!0,coerce:!0},a==null?void 0:a.spec),this.withMutation(r=>{r.nonNullable()})}get _type(){return this.type}clone(a){if(this._mutate)return a&&Object.assign(this.spec,a),this;const r=Object.create(Object.getPrototypeOf(this));return r.type=this.type,r._typeCheck=this._typeCheck,r._whitelist=this._whitelist.clone(),r._blacklist=this._blacklist.clone(),r.internalTests=Object.assign({},this.internalTests),r.exclusiveTests=Object.assign({},this.exclusiveTests),r.deps=[...this.deps],r.conditions=[...this.conditions],r.tests=[...this.tests],r.transforms=[...this.transforms],r.spec=xi(Object.assign({},this.spec,a)),r}label(a){let r=this.clone();return r.spec.label=a,r}meta(...a){if(a.length===0)return this.spec.meta;let r=this.clone();return r.spec.meta=Object.assign(r.spec.meta||{},a[0]),r}withMutation(a){let r=this._mutate;this._mutate=!0;let s=a(this);return this._mutate=r,s}concat(a){if(!a||a===this)return this;if(a.type!==this.type&&this.type!=="mixed")throw new TypeError(`You cannot \`concat()\` schema's of different types: ${this.type} and ${a.type}`);let r=this,s=a.clone();const o=Object.assign({},r.spec,s.spec);return s.spec=o,s.internalTests=Object.assign({},r.internalTests,s.internalTests),s._whitelist=r._whitelist.merge(a._whitelist,a._blacklist),s._blacklist=r._blacklist.merge(a._blacklist,a._whitelist),s.tests=r.tests,s.exclusiveTests=r.exclusiveTests,s.withMutation(f=>{a.tests.forEach(d=>{f.test(d.OPTIONS)})}),s.transforms=[...r.transforms,...s.transforms],s}isType(a){return a==null?!!(this.spec.nullable&&a===null||this.spec.optional&&a===void 0):this._typeCheck(a)}resolve(a){let r=this;if(r.conditions.length){let s=r.conditions;r=r.clone(),r.conditions=[],r=s.reduce((o,f)=>f.resolve(o,a),r),r=r.resolve(a)}return r}resolveOptions(a){var r,s,o,f;return Object.assign({},a,{from:a.from||[],strict:(r=a.strict)!=null?r:this.spec.strict,abortEarly:(s=a.abortEarly)!=null?s:this.spec.abortEarly,recursive:(o=a.recursive)!=null?o:this.spec.recursive,disableStackTrace:(f=a.disableStackTrace)!=null?f:this.spec.disableStackTrace})}cast(a,r={}){let s=this.resolve(Object.assign({value:a},r)),o=r.assert==="ignore-optionality",f=s._cast(a,r);if(r.assert!==!1&&!s.isType(f)){if(o&&Oa(f))return f;let d=sa(a),h=sa(f);throw new TypeError(`The value of ${r.path||"field"} could not be cast to a value that satisfies the schema type: "${s.type}". 

attempted value: ${d} 
`+(h!==d?`result of cast: ${h}`:""))}return f}_cast(a,r){let s=a===void 0?a:this.transforms.reduce((o,f)=>f.call(this,o,a,this),a);return s===void 0&&(s=this.getDefault(r)),s}_validate(a,r={},s,o){let{path:f,originalValue:d=a,strict:h=this.spec.strict}=r,p=a;h||(p=this._cast(p,Object.assign({assert:!1},r)));let m=[];for(let g of Object.values(this.internalTests))g&&m.push(g);this.runTests({path:f,value:p,originalValue:d,options:r,tests:m},s,g=>{if(g.length)return o(g,p);this.runTests({path:f,value:p,originalValue:d,options:r,tests:this.tests},s,o)})}runTests(a,r,s){let o=!1,{tests:f,value:d,originalValue:h,path:p,options:m}=a,g=C=>{o||(o=!0,r(C,d))},S=C=>{o||(o=!0,s(C,d))},x=f.length,R=[];if(!x)return S([]);let E={value:d,originalValue:h,path:p,options:m,schema:this};for(let C=0;C<f.length;C++){const D=f[C];D(E,g,function(T){T&&(Array.isArray(T)?R.push(...T):R.push(T)),--x<=0&&S(R)})}}asNestedTest({key:a,index:r,parent:s,parentPath:o,originalParent:f,options:d}){const h=a??r;if(h==null)throw TypeError("Must include `key` or `index` for nested validations");const p=typeof h=="number";let m=s[h];const g=Object.assign({},d,{strict:!0,parent:s,value:m,originalValue:f[h],key:void 0,[p?"index":"key"]:h,path:p||h.includes(".")?`${o||""}[${p?h:`"${h}"`}]`:(o?`${o}.`:"")+a});return(S,x,R)=>this.resolve(g)._validate(m,g,x,R)}validate(a,r){var s;let o=this.resolve(Object.assign({},r,{value:a})),f=(s=r==null?void 0:r.disableStackTrace)!=null?s:o.spec.disableStackTrace;return new Promise((d,h)=>o._validate(a,r,(p,m)=>{Wt.isError(p)&&(p.value=m),h(p)},(p,m)=>{p.length?h(new Wt(p,m,void 0,void 0,f)):d(m)}))}validateSync(a,r){var s;let o=this.resolve(Object.assign({},r,{value:a})),f,d=(s=r==null?void 0:r.disableStackTrace)!=null?s:o.spec.disableStackTrace;return o._validate(a,Object.assign({},r,{sync:!0}),(h,p)=>{throw Wt.isError(h)&&(h.value=p),h},(h,p)=>{if(h.length)throw new Wt(h,a,void 0,void 0,d);f=p}),f}isValid(a,r){return this.validate(a,r).then(()=>!0,s=>{if(Wt.isError(s))return!1;throw s})}isValidSync(a,r){try{return this.validateSync(a,r),!0}catch(s){if(Wt.isError(s))return!1;throw s}}_getDefault(a){let r=this.spec.default;return r==null?r:typeof r=="function"?r.call(this,a):xi(r)}getDefault(a){return this.resolve(a||{})._getDefault(a)}default(a){return arguments.length===0?this._getDefault():this.clone({default:a})}strict(a=!0){return this.clone({strict:a})}nullability(a,r){const s=this.clone({nullable:a});return s.internalTests.nullable=bi({message:r,name:"nullable",test(o){return o===null?this.schema.spec.nullable:!0}}),s}optionality(a,r){const s=this.clone({optional:a});return s.internalTests.optionality=bi({message:r,name:"optionality",test(o){return o===void 0?this.schema.spec.optional:!0}}),s}optional(){return this.optionality(!0)}defined(a=tl.defined){return this.optionality(!1,a)}nullable(){return this.nullability(!0)}nonNullable(a=tl.notNull){return this.nullability(!1,a)}required(a=tl.required){return this.clone().withMutation(r=>r.nonNullable(a).defined(a))}notRequired(){return this.clone().withMutation(a=>a.nullable().optional())}transform(a){let r=this.clone();return r.transforms.push(a),r}test(...a){let r;if(a.length===1?typeof a[0]=="function"?r={test:a[0]}:r=a[0]:a.length===2?r={name:a[0],test:a[1]}:r={name:a[0],message:a[1],test:a[2]},r.message===void 0&&(r.message=tl.default),typeof r.test!="function")throw new TypeError("`test` is a required parameters");let s=this.clone(),o=bi(r),f=r.exclusive||r.name&&s.exclusiveTests[r.name]===!0;if(r.exclusive&&!r.name)throw new TypeError("Exclusive tests must provide a unique `name` identifying the test");return r.name&&(s.exclusiveTests[r.name]=!!r.exclusive),s.tests=s.tests.filter(d=>!(d.OPTIONS.name===r.name&&(f||d.OPTIONS.test===o.OPTIONS.test))),s.tests.push(o),s}when(a,r){!Array.isArray(a)&&typeof a!="string"&&(r=a,a=".");let s=this.clone(),o=Ty(a).map(f=>new oa(f));return o.forEach(f=>{f.isSibling&&s.deps.push(f.key)}),s.conditions.push(typeof r=="function"?new wu(o,r):wu.fromOptions(o,r)),s}typeError(a){let r=this.clone();return r.internalTests.typeError=bi({message:a,name:"typeError",skipAbsent:!0,test(s){return this.schema._typeCheck(s)?!0:this.createError({params:{type:this.schema.type}})}}),r}oneOf(a,r=tl.oneOf){let s=this.clone();return a.forEach(o=>{s._whitelist.add(o),s._blacklist.delete(o)}),s.internalTests.whiteList=bi({message:r,name:"oneOf",skipAbsent:!0,test(o){let f=this.schema._whitelist,d=f.resolveAll(this.resolve);return d.includes(o)?!0:this.createError({params:{values:Array.from(f).join(", "),resolved:d}})}}),s}notOneOf(a,r=tl.notOneOf){let s=this.clone();return a.forEach(o=>{s._blacklist.add(o),s._whitelist.delete(o)}),s.internalTests.blacklist=bi({message:r,name:"notOneOf",test(o){let f=this.schema._blacklist,d=f.resolveAll(this.resolve);return d.includes(o)?this.createError({params:{values:Array.from(f).join(", "),resolved:d}}):!0}}),s}strip(a=!0){let r=this.clone();return r.spec.strip=a,r}describe(a){const r=(a?this.resolve(a):this).clone(),{label:s,meta:o,optional:f,nullable:d}=r.spec;return{meta:o,label:s,optional:f,nullable:d,default:r.getDefault(a),type:r.type,oneOf:r._whitelist.describe(),notOneOf:r._blacklist.describe(),tests:r.tests.map(p=>({name:p.OPTIONS.name,params:p.OPTIONS.params})).filter((p,m,g)=>g.findIndex(S=>S.name===p.name)===m)}}}il.prototype.__isYupSchema__=!0;for(const l of["validate","validateSync"])il.prototype[`${l}At`]=function(a,r,s={}){const{parent:o,parentPath:f,schema:d}=IS(this,a,r,s.context);return d[l](o&&o[f],Object.assign({},s,{parent:o,path:a}))};for(const l of["equals","is"])il.prototype[l]=il.prototype.oneOf;for(const l of["not","nope"])il.prototype[l]=il.prototype.notOneOf;const ex=/^(\d{4}|[+-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,.](\d{1,}))?)?(?:(Z)|([+-])(\d{2})(?::?(\d{2}))?)?)?$/;function tx(l){const a=Of(l);if(!a)return Date.parse?Date.parse(l):Number.NaN;if(a.z===void 0&&a.plusMinus===void 0)return new Date(a.year,a.month,a.day,a.hour,a.minute,a.second,a.millisecond).valueOf();let r=0;return a.z!=="Z"&&a.plusMinus!==void 0&&(r=a.hourOffset*60+a.minuteOffset,a.plusMinus==="+"&&(r=0-r)),Date.UTC(a.year,a.month,a.day,a.hour,a.minute+r,a.second,a.millisecond)}function Of(l){var a,r;const s=ex.exec(l);return s?{year:Cl(s[1]),month:Cl(s[2],1)-1,day:Cl(s[3],1),hour:Cl(s[4]),minute:Cl(s[5]),second:Cl(s[6]),millisecond:s[7]?Cl(s[7].substring(0,3)):0,precision:(a=(r=s[7])==null?void 0:r.length)!=null?a:void 0,z:s[8]||void 0,plusMinus:s[9]||void 0,hourOffset:Cl(s[10]),minuteOffset:Cl(s[11])}:null}function Cl(l,a=0){return Number(l)||a}let nx=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,lx=/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,ax=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,ix="^\\d{4}-\\d{2}-\\d{2}",rx="\\d{2}:\\d{2}:\\d{2}",sx="(([+-]\\d{2}(:?\\d{2})?)|Z)",ux=new RegExp(`${ix}T${rx}(\\.\\d+)?${sx}$`),ox=l=>Oa(l)||l===l.trim(),cx={}.toString();function It(){return new Dy}class Dy extends il{constructor(){super({type:"string",check(a){return a instanceof String&&(a=a.valueOf()),typeof a=="string"}}),this.withMutation(()=>{this.transform((a,r,s)=>{if(!s.spec.coerce||s.isType(a)||Array.isArray(a))return a;const o=a!=null&&a.toString?a.toString():a;return o===cx?a:o})})}required(a){return super.required(a).withMutation(r=>r.test({message:a||tl.required,name:"required",skipAbsent:!0,test:s=>!!s.length}))}notRequired(){return super.notRequired().withMutation(a=>(a.tests=a.tests.filter(r=>r.OPTIONS.name!=="required"),a))}length(a,r=Pt.length){return this.test({message:r,name:"length",exclusive:!0,params:{length:a},skipAbsent:!0,test(s){return s.length===this.resolve(a)}})}min(a,r=Pt.min){return this.test({message:r,name:"min",exclusive:!0,params:{min:a},skipAbsent:!0,test(s){return s.length>=this.resolve(a)}})}max(a,r=Pt.max){return this.test({name:"max",exclusive:!0,message:r,params:{max:a},skipAbsent:!0,test(s){return s.length<=this.resolve(a)}})}matches(a,r){let s=!1,o,f;return r&&(typeof r=="object"?{excludeEmptyString:s=!1,message:o,name:f}=r:o=r),this.test({name:f||"matches",message:o||Pt.matches,params:{regex:a},skipAbsent:!0,test:d=>d===""&&s||d.search(a)!==-1})}email(a=Pt.email){return this.matches(nx,{name:"email",message:a,excludeEmptyString:!0})}url(a=Pt.url){return this.matches(lx,{name:"url",message:a,excludeEmptyString:!0})}uuid(a=Pt.uuid){return this.matches(ax,{name:"uuid",message:a,excludeEmptyString:!1})}datetime(a){let r="",s,o;return a&&(typeof a=="object"?{message:r="",allowOffset:s=!1,precision:o=void 0}=a:r=a),this.matches(ux,{name:"datetime",message:r||Pt.datetime,excludeEmptyString:!0}).test({name:"datetime_offset",message:r||Pt.datetime_offset,params:{allowOffset:s},skipAbsent:!0,test:f=>{if(!f||s)return!0;const d=Of(f);return d?!!d.z:!1}}).test({name:"datetime_precision",message:r||Pt.datetime_precision,params:{precision:o},skipAbsent:!0,test:f=>{if(!f||o==null)return!0;const d=Of(f);return d?d.precision===o:!1}})}ensure(){return this.default("").transform(a=>a===null?"":a)}trim(a=Pt.trim){return this.transform(r=>r!=null?r.trim():r).test({message:a,name:"trim",test:ox})}lowercase(a=Pt.lowercase){return this.transform(r=>Oa(r)?r:r.toLowerCase()).test({message:a,name:"string_case",exclusive:!0,skipAbsent:!0,test:r=>Oa(r)||r===r.toLowerCase()})}uppercase(a=Pt.uppercase){return this.transform(r=>Oa(r)?r:r.toUpperCase()).test({message:a,name:"string_case",exclusive:!0,skipAbsent:!0,test:r=>Oa(r)||r===r.toUpperCase()})}}It.prototype=Dy.prototype;let fx=new Date(""),dx=l=>Object.prototype.toString.call(l)==="[object Date]";class td extends il{constructor(){super({type:"date",check(a){return dx(a)&&!isNaN(a.getTime())}}),this.withMutation(()=>{this.transform((a,r,s)=>!s.spec.coerce||s.isType(a)||a===null?a:(a=tx(a),isNaN(a)?td.INVALID_DATE:new Date(a)))})}prepareParam(a,r){let s;if(oa.isRef(a))s=a;else{let o=this.cast(a);if(!this._typeCheck(o))throw new TypeError(`\`${r}\` must be a Date or a value that can be \`cast()\` to a Date`);s=o}return s}min(a,r=Rf.min){let s=this.prepareParam(a,"min");return this.test({message:r,name:"min",exclusive:!0,params:{min:a},skipAbsent:!0,test(o){return o>=this.resolve(s)}})}max(a,r=Rf.max){let s=this.prepareParam(a,"max");return this.test({message:r,name:"max",exclusive:!0,params:{max:a},skipAbsent:!0,test(o){return o<=this.resolve(s)}})}}td.INVALID_DATE=fx;function hx(l,a=[]){let r=[],s=new Set,o=new Set(a.map(([d,h])=>`${d}-${h}`));function f(d,h){let p=Da.split(d)[0];s.add(p),o.has(`${h}-${p}`)||r.push([h,p])}for(const d of Object.keys(l)){let h=l[d];s.add(d),oa.isRef(h)&&h.isSibling?f(h.path,d):If(h)&&"deps"in h&&h.deps.forEach(p=>f(p,d))}return VS.array(Array.from(s),r).reverse()}function A0(l,a){let r=1/0;return l.some((s,o)=>{var f;if((f=a.path)!=null&&f.includes(s))return r=o,!0}),r}function Cy(l){return(a,r)=>A0(l,a)-A0(l,r)}const mx=(l,a,r)=>{if(typeof l!="string")return l;let s=l;try{s=JSON.parse(l)}catch{}return r.isType(s)?s:l};function pu(l){if("fields"in l){const a={};for(const[r,s]of Object.entries(l.fields))a[r]=pu(s);return l.setFields(a)}if(l.type==="array"){const a=l.optional();return a.innerType&&(a.innerType=pu(a.innerType)),a}return l.type==="tuple"?l.optional().clone({types:l.spec.types.map(pu)}):"optional"in l?l.optional():l}const px=(l,a)=>{const r=[...Da.normalizePath(a)];if(r.length===1)return r[0]in l;let s=r.pop(),o=Da.getter(Da.join(r),!0)(l);return!!(o&&s in o)};let _0=l=>Object.prototype.toString.call(l)==="[object Object]";function T0(l,a){let r=Object.keys(l.fields);return Object.keys(a).filter(s=>r.indexOf(s)===-1)}const yx=Cy([]);function ji(l){return new Ny(l)}class Ny extends il{constructor(a){super({type:"object",check(r){return _0(r)||typeof r=="function"}}),this.fields=Object.create(null),this._sortErrors=yx,this._nodes=[],this._excludedEdges=[],this.withMutation(()=>{a&&this.shape(a)})}_cast(a,r={}){var s;let o=super._cast(a,r);if(o===void 0)return this.getDefault(r);if(!this._typeCheck(o))return o;let f=this.fields,d=(s=r.stripUnknown)!=null?s:this.spec.noUnknown,h=[].concat(this._nodes,Object.keys(o).filter(S=>!this._nodes.includes(S))),p={},m=Object.assign({},r,{parent:p,__validating:r.__validating||!1}),g=!1;for(const S of h){let x=f[S],R=S in o;if(x){let E,C=o[S];m.path=(r.path?`${r.path}.`:"")+S,x=x.resolve({value:C,context:r.context,parent:p});let D=x instanceof il?x.spec:void 0,w=D==null?void 0:D.strict;if(D!=null&&D.strip){g=g||S in o;continue}E=!r.__validating||!w?x.cast(o[S],m):o[S],E!==void 0&&(p[S]=E)}else R&&!d&&(p[S]=o[S]);(R!==S in p||p[S]!==o[S])&&(g=!0)}return g?p:o}_validate(a,r={},s,o){let{from:f=[],originalValue:d=a,recursive:h=this.spec.recursive}=r;r.from=[{schema:this,value:d},...f],r.__validating=!0,r.originalValue=d,super._validate(a,r,s,(p,m)=>{if(!h||!_0(m)){o(p,m);return}d=d||m;let g=[];for(let S of this._nodes){let x=this.fields[S];!x||oa.isRef(x)||g.push(x.asNestedTest({options:r,key:S,parent:m,parentPath:r.path,originalParent:d}))}this.runTests({tests:g,value:m,originalValue:d,options:r},s,S=>{o(S.sort(this._sortErrors).concat(p),m)})})}clone(a){const r=super.clone(a);return r.fields=Object.assign({},this.fields),r._nodes=this._nodes,r._excludedEdges=this._excludedEdges,r._sortErrors=this._sortErrors,r}concat(a){let r=super.concat(a),s=r.fields;for(let[o,f]of Object.entries(this.fields)){const d=s[o];s[o]=d===void 0?f:d}return r.withMutation(o=>o.setFields(s,[...this._excludedEdges,...a._excludedEdges]))}_getDefault(a){if("default"in this.spec)return super._getDefault(a);if(!this._nodes.length)return;let r={};return this._nodes.forEach(s=>{var o;const f=this.fields[s];let d=a;(o=d)!=null&&o.value&&(d=Object.assign({},d,{parent:d.value,value:d.value[s]})),r[s]=f&&"getDefault"in f?f.getDefault(d):void 0}),r}setFields(a,r){let s=this.clone();return s.fields=a,s._nodes=hx(a,r),s._sortErrors=Cy(Object.keys(a)),r&&(s._excludedEdges=r),s}shape(a,r=[]){return this.clone().withMutation(s=>{let o=s._excludedEdges;return r.length&&(Array.isArray(r[0])||(r=[r]),o=[...s._excludedEdges,...r]),s.setFields(Object.assign(s.fields,a),o)})}partial(){const a={};for(const[r,s]of Object.entries(this.fields))a[r]="optional"in s&&s.optional instanceof Function?s.optional():s;return this.setFields(a)}deepPartial(){return pu(this)}pick(a){const r={};for(const s of a)this.fields[s]&&(r[s]=this.fields[s]);return this.setFields(r,this._excludedEdges.filter(([s,o])=>a.includes(s)&&a.includes(o)))}omit(a){const r=[];for(const s of Object.keys(this.fields))a.includes(s)||r.push(s);return this.pick(r)}from(a,r,s){let o=Da.getter(a,!0);return this.transform(f=>{if(!f)return f;let d=f;return px(f,a)&&(d=Object.assign({},f),s||delete d[a],d[r]=o(f)),d})}json(){return this.transform(mx)}exact(a){return this.test({name:"exact",exclusive:!0,message:a||mu.exact,test(r){if(r==null)return!0;const s=T0(this.schema,r);return s.length===0||this.createError({params:{properties:s.join(", ")}})}})}stripUnknown(){return this.clone({noUnknown:!0})}noUnknown(a=!0,r=mu.noUnknown){typeof a!="boolean"&&(r=a,a=!0);let s=this.test({name:"noUnknown",exclusive:!0,message:r,test(o){if(o==null)return!0;const f=T0(this.schema,o);return!a||f.length===0||this.createError({params:{unknown:f.join(", ")}})}});return s.spec.noUnknown=a,s}unknown(a=!0,r=mu.noUnknown){return this.noUnknown(!a,r)}transformKeys(a){return this.transform(r=>{if(!r)return r;const s={};for(const o of Object.keys(r))s[a(o)]=r[o];return s})}camelCase(){return this.transformKeys(hf.camelCase)}snakeCase(){return this.transformKeys(hf.snakeCase)}constantCase(){return this.transformKeys(a=>hf.snakeCase(a).toUpperCase())}describe(a){const r=(a?this.resolve(a):this).clone(),s=super.describe(a);s.fields={};for(const[f,d]of Object.entries(r.fields)){var o;let h=a;(o=h)!=null&&o.value&&(h=Object.assign({},h,{parent:h.value,value:h.value[f]})),s.fields[f]=d.describe(h)}return s}}ji.prototype=Ny.prototype;const R0=(l,a,r)=>{if(l&&"reportValidity"in l){const s=ce(r,a);l.setCustomValidity(s&&s.message||""),l.reportValidity()}},My=(l,a)=>{for(const r in a.fields){const s=a.fields[r];s&&s.ref&&"reportValidity"in s.ref?R0(s.ref,r,l):s&&s.refs&&s.refs.forEach(o=>R0(o,r,l))}},gx=(l,a)=>{a.shouldUseNativeValidation&&My(l,a);const r={};for(const s in l){const o=ce(a.fields,s),f=Object.assign(l[s]||{},{ref:o&&o.ref});if(vx(a.names||Object.keys(l),s)){const d=Object.assign({},ce(r,s));Qe(d,"root",f),Qe(r,s,d)}else Qe(r,s,f)}return r},vx=(l,a)=>{const r=O0(a);return l.some(s=>O0(s).match(`^${r}\\.\\d+`))};function O0(l){return l.replace(/\]|\[/g,"")}function Xr(l,a,r){return r===void 0&&(r={}),function(s,o,f){try{return Promise.resolve(function(d,h){try{var p=(a!=null&&a.context,Promise.resolve(l[r.mode==="sync"?"validateSync":"validate"](s,Object.assign({abortEarly:!1},a,{context:o}))).then(function(m){return f.shouldUseNativeValidation&&My({},f),{values:r.raw?Object.assign({},s):m,errors:{}}}))}catch(m){return h(m)}return p&&p.then?p.then(void 0,h):p}(0,function(d){if(!d.inner)throw d;return{values:{},errors:gx((h=d,p=!f.shouldUseNativeValidation&&f.criteriaMode==="all",(h.inner||[]).reduce(function(m,g){if(m[g.path]||(m[g.path]={message:g.message,type:g.type}),p){var S=m[g.path].types,x=S&&S[g.type];m[g.path]=by(g.path,p,m,g.type,x?[].concat(x,g.message):g.message)}return m},{})),f)};var h,p}))}catch(d){return Promise.reject(d)}}}const mf="http://127.0.0.1:8000/auth/",bx="http://127.0.0.1:8000/users/";class Sx{async login(a,r){try{const s=await fetch(mf+"jwt/create/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:a,password:r})});if(!s.ok)return console.log("login failed!"),!1;const o=await s.json();if(o.access){localStorage.setItem("access_token",o.access),localStorage.setItem("refresh_token",o.refresh),console.log("token stored in the local storage!");const f=await this.getCurrentUser();sessionStorage.setItem("current_user",JSON.stringify(f))}else console.log("No access token in response!");return!0}catch(s){throw console.error("Login failed",s),s}}async resetPassword(a){try{const r=await fetch(mf+"users/reset_password/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:a})});return r.ok?!0:(console.log("Reset password response:",r.status),!1)}catch(r){console.log("Failed to reset password!",r)}}async resetPasswordConfirm(a,r,s){try{const o=await fetch(mf+"users/reset_password_confirm/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({uid:a,token:r,new_password:s})});return o.ok?!0:(console.log("Reset password confirm response:",o.status),!1)}catch(o){console.log("Failed to reset password confirm!",o)}}async getCurrentUser(){try{console.log("Getting current user...");const a=await fetch(bx+"me/",{method:"GET",headers:this.getAuthHeader()});if(!a.ok){const s=await a.json();return console.log("Error data: ",s),null}return await a.json()}catch(a){console.log("Failed to get the current user!",a)}}getAccessToken(){return console.log("Got token: ",localStorage.getItem("access_token")),localStorage.getItem("access_token")}getAuthHeader(){const a=this.getAccessToken();return console.log("Got token in getAuthHeader: ",a),{"Content-Type":"application/json",Accept:"application/json",Authorization:a?`JWT ${a}`:""}}logout(){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),sessionStorage.removeItem("current_user")}}const Ml=new Sx;function Di(){return v.jsxs("div",{className:"loading-ripple",children:[v.jsx("div",{className:"circle-1"}),v.jsx("div",{className:"circle-2"}),v.jsx("div",{className:"circle-3"}),v.jsx("div",{className:"circle-4"})]})}const zu="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Close'%20x='0'%20y='0'%20version='1.1'%20viewBox='0%200%20512%20512'%3e%3cpath%20d='M437.5%20386.6L306.9%20256l130.6-130.6c14.1-14.1%2014.1-36.8%200-50.9-14.1-14.1-36.8-14.1-50.9%200L256%20205.1%20125.4%2074.5c-14.1-14.1-36.8-14.1-50.9%200-14.1%2014.1-14.1%2036.8%200%2050.9L205.1%20256%2074.5%20386.6c-14.1%2014.1-14.1%2036.8%200%2050.9%2014.1%2014.1%2036.8%2014.1%2050.9%200L256%20306.9l130.6%20130.6c14.1%2014.1%2036.8%2014.1%2050.9%200%2014-14.1%2014-36.9%200-50.9z'%20fill='%23f5f5f9'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e",xx="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Check'%20x='0'%20y='0'%20version='1.1'%20viewBox='0%200%2020%2020'%3e%3cpath%20d='M8.294%2016.998c-.435%200-.847-.203-1.111-.553L3.61%2011.724a1.392%201.392%200%200%201%20.27-1.951%201.392%201.392%200%200%201%201.953.27l2.351%203.104%205.911-9.492a1.396%201.396%200%200%201%201.921-.445c.653.406.854%201.266.446%201.92L9.478%2016.34a1.39%201.39%200%200%201-1.12.656c-.022.002-.042.002-.064.002z'%20fill='%23fcfcfc'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e";function Hr({message:l,type:a}){let r=a==="success"?xx:zu;return v.jsxs("div",{className:`alert alert-${a}`,children:[v.jsx("img",{src:r,alt:"close-icon"}),v.jsx("p",{children:l})]})}function Ex(){const l=Vn(),[a,r]=_.useState(!1),[s,o]=_.useState(null);_.useEffect(()=>{s&&setTimeout(()=>{o(!1)},5e3)},[s]);const f=ji().shape({email:It().required("Must not empty.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format."),password:It().required("Must not empty.")}),{register:d,handleSubmit:h,formState:{errors:p,isValid:m}}=$r({resolver:Xr(f),mode:"all"}),g=async S=>{r(!0);try{await Ml.login(S.email,S.password)?(console.log("Login successfully!"),l("/home")):o(!0)}catch(x){console.log("login failed!",x)}finally{r(!1)}};return v.jsxs(v.Fragment,{children:[s&&v.jsx(Hr,{message:"Invalid credentials.",type:"danger"}),v.jsxs("main",{className:"login-page",children:[v.jsx("section",{className:"left-panel",children:v.jsx("img",{src:pS,alt:"log-in"})}),v.jsxs("section",{className:"right-panel",children:[v.jsx("img",{src:yS,alt:"logo"}),v.jsxs("form",{onSubmit:h(g),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),p.email&&v.jsx("span",{children:p.email.message}),v.jsx("input",{type:"email",name:"email",id:"email",required:!0,placeholder:"Enter email",...d("email")})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"Password:"}),p.password&&v.jsx("span",{children:p.password.message}),v.jsx("input",{type:"password",name:"password",id:"password",required:!0,placeholder:"Enter password",...d("password")})]}),v.jsxs("button",{type:"submit",disabled:!m||a,className:"submit-button",children:[a&&v.jsx(Di,{}),a?"Verifying...":"Log In"]})]}),v.jsx("a",{onClick:()=>l("/reset-password"),children:"Forgot Password?"}),v.jsx("a",{onClick:()=>l("/register"),children:"Create new account"})]})]})]})}const _u="/assets/do-QzNztaLT.png",wx="data:image/svg+xml,%3csvg%20width='33'%20height='34'%20viewBox='0%200%2033%2034'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='20.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3cpath%20d='M18.5%2012L21.5%2016'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M26.5%209.5L23.5%2015.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3ccircle%20cx='17'%20cy='10.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3ccircle%20cx='23'%20cy='17.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3ccircle%20cx='27'%20cy='7.5'%20r='2'%20stroke='%23FFD700'%20stroke-width='2'/%3e%3cpath%20d='M5.5%2024L10%2021.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M13%2018.5L16%2012.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M27%2024.5L31.5326%2028.5793C31.7717%2028.7945%2031.7483%2029.1762%2031.4849%2029.3606L27%2032.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M30%2028.5L5%2028.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M1%206.5L5.07934%201.9674C5.2945%201.72833%205.67616%201.75165%205.8606%202.01515L9%206.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3cpath%20d='M5%203.5L5%2028.5'%20stroke='%23FFD700'%20stroke-width='2'%20stroke-linecap='round'/%3e%3c/svg%3e",wi=Math.min,Ca=Math.max,Tu=Math.round,cu=Math.floor,al=l=>({x:l,y:l}),Ax={left:"right",right:"left",bottom:"top",top:"bottom"},_x={start:"end",end:"start"};function jf(l,a,r){return Ca(l,wi(a,r))}function Zr(l,a){return typeof l=="function"?l(a):l}function Ma(l){return l.split("-")[0]}function Qr(l){return l.split("-")[1]}function Uy(l){return l==="x"?"y":"x"}function nd(l){return l==="y"?"height":"width"}function Ai(l){return["top","bottom"].includes(Ma(l))?"y":"x"}function ld(l){return Uy(Ai(l))}function Tx(l,a,r){r===void 0&&(r=!1);const s=Qr(l),o=ld(l),f=nd(o);let d=o==="x"?s===(r?"end":"start")?"right":"left":s==="start"?"bottom":"top";return a.reference[f]>a.floating[f]&&(d=Ru(d)),[d,Ru(d)]}function Rx(l){const a=Ru(l);return[Df(l),a,Df(a)]}function Df(l){return l.replace(/start|end/g,a=>_x[a])}function Ox(l,a,r){const s=["left","right"],o=["right","left"],f=["top","bottom"],d=["bottom","top"];switch(l){case"top":case"bottom":return r?a?o:s:a?s:o;case"left":case"right":return a?f:d;default:return[]}}function jx(l,a,r,s){const o=Qr(l);let f=Ox(Ma(l),r==="start",s);return o&&(f=f.map(d=>d+"-"+o),a&&(f=f.concat(f.map(Df)))),f}function Ru(l){return l.replace(/left|right|bottom|top/g,a=>Ax[a])}function Dx(l){return{top:0,right:0,bottom:0,left:0,...l}}function zy(l){return typeof l!="number"?Dx(l):{top:l,right:l,bottom:l,left:l}}function Ou(l){const{x:a,y:r,width:s,height:o}=l;return{width:s,height:o,top:r,left:a,right:a+s,bottom:r+o,x:a,y:r}}function j0(l,a,r){let{reference:s,floating:o}=l;const f=Ai(a),d=ld(a),h=nd(d),p=Ma(a),m=f==="y",g=s.x+s.width/2-o.width/2,S=s.y+s.height/2-o.height/2,x=s[h]/2-o[h]/2;let R;switch(p){case"top":R={x:g,y:s.y-o.height};break;case"bottom":R={x:g,y:s.y+s.height};break;case"right":R={x:s.x+s.width,y:S};break;case"left":R={x:s.x-o.width,y:S};break;default:R={x:s.x,y:s.y}}switch(Qr(a)){case"start":R[d]-=x*(r&&m?-1:1);break;case"end":R[d]+=x*(r&&m?-1:1);break}return R}const Cx=async(l,a,r)=>{const{placement:s="bottom",strategy:o="absolute",middleware:f=[],platform:d}=r,h=f.filter(Boolean),p=await(d.isRTL==null?void 0:d.isRTL(a));let m=await d.getElementRects({reference:l,floating:a,strategy:o}),{x:g,y:S}=j0(m,s,p),x=s,R={},E=0;for(let C=0;C<h.length;C++){const{name:D,fn:w}=h[C],{x:T,y:M,data:X,reset:V}=await w({x:g,y:S,initialPlacement:s,placement:x,strategy:o,middlewareData:R,rects:m,platform:d,elements:{reference:l,floating:a}});g=T??g,S=M??S,R={...R,[D]:{...R[D],...X}},V&&E<=50&&(E++,typeof V=="object"&&(V.placement&&(x=V.placement),V.rects&&(m=V.rects===!0?await d.getElementRects({reference:l,floating:a,strategy:o}):V.rects),{x:g,y:S}=j0(m,x,p)),C=-1)}return{x:g,y:S,placement:x,strategy:o,middlewareData:R}};async function Ly(l,a){var r;a===void 0&&(a={});const{x:s,y:o,platform:f,rects:d,elements:h,strategy:p}=l,{boundary:m="clippingAncestors",rootBoundary:g="viewport",elementContext:S="floating",altBoundary:x=!1,padding:R=0}=Zr(a,l),E=zy(R),D=h[x?S==="floating"?"reference":"floating":S],w=Ou(await f.getClippingRect({element:(r=await(f.isElement==null?void 0:f.isElement(D)))==null||r?D:D.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(h.floating)),boundary:m,rootBoundary:g,strategy:p})),T=S==="floating"?{x:s,y:o,width:d.floating.width,height:d.floating.height}:d.reference,M=await(f.getOffsetParent==null?void 0:f.getOffsetParent(h.floating)),X=await(f.isElement==null?void 0:f.isElement(M))?await(f.getScale==null?void 0:f.getScale(M))||{x:1,y:1}:{x:1,y:1},V=Ou(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:T,offsetParent:M,strategy:p}):T);return{top:(w.top-V.top+E.top)/X.y,bottom:(V.bottom-w.bottom+E.bottom)/X.y,left:(w.left-V.left+E.left)/X.x,right:(V.right-w.right+E.right)/X.x}}const Nx=l=>({name:"arrow",options:l,async fn(a){const{x:r,y:s,placement:o,rects:f,platform:d,elements:h,middlewareData:p}=a,{element:m,padding:g=0}=Zr(l,a)||{};if(m==null)return{};const S=zy(g),x={x:r,y:s},R=ld(o),E=nd(R),C=await d.getDimensions(m),D=R==="y",w=D?"top":"left",T=D?"bottom":"right",M=D?"clientHeight":"clientWidth",X=f.reference[E]+f.reference[R]-x[R]-f.floating[E],V=x[R]-f.reference[R],I=await(d.getOffsetParent==null?void 0:d.getOffsetParent(m));let W=I?I[M]:0;(!W||!await(d.isElement==null?void 0:d.isElement(I)))&&(W=h.floating[M]||f.floating[E]);const se=X/2-V/2,fe=W/2-C[E]/2-1,De=wi(S[w],fe),ge=wi(S[T],fe),re=De,ye=W-C[E]-ge,de=W/2-C[E]/2+se,_e=jf(re,de,ye),H=!p.arrow&&Qr(o)!=null&&de!==_e&&f.reference[E]/2-(de<re?De:ge)-C[E]/2<0,J=H?de<re?de-re:de-ye:0;return{[R]:x[R]+J,data:{[R]:_e,centerOffset:de-_e-J,...H&&{alignmentOffset:J}},reset:H}}}),Mx=function(l){return l===void 0&&(l={}),{name:"flip",options:l,async fn(a){var r,s;const{placement:o,middlewareData:f,rects:d,initialPlacement:h,platform:p,elements:m}=a,{mainAxis:g=!0,crossAxis:S=!0,fallbackPlacements:x,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:C=!0,...D}=Zr(l,a);if((r=f.arrow)!=null&&r.alignmentOffset)return{};const w=Ma(o),T=Ai(h),M=Ma(h)===h,X=await(p.isRTL==null?void 0:p.isRTL(m.floating)),V=x||(M||!C?[Ru(h)]:Rx(h)),I=E!=="none";!x&&I&&V.push(...jx(h,C,E,X));const W=[h,...V],se=await Ly(a,D),fe=[];let De=((s=f.flip)==null?void 0:s.overflows)||[];if(g&&fe.push(se[w]),S){const de=Tx(o,d,X);fe.push(se[de[0]],se[de[1]])}if(De=[...De,{placement:o,overflows:fe}],!fe.every(de=>de<=0)){var ge,re;const de=(((ge=f.flip)==null?void 0:ge.index)||0)+1,_e=W[de];if(_e)return{data:{index:de,overflows:De},reset:{placement:_e}};let H=(re=De.filter(J=>J.overflows[0]<=0).sort((J,te)=>J.overflows[1]-te.overflows[1])[0])==null?void 0:re.placement;if(!H)switch(R){case"bestFit":{var ye;const J=(ye=De.filter(te=>{if(I){const we=Ai(te.placement);return we===T||we==="y"}return!0}).map(te=>[te.placement,te.overflows.filter(we=>we>0).reduce((we,O)=>we+O,0)]).sort((te,we)=>te[1]-we[1])[0])==null?void 0:ye[0];J&&(H=J);break}case"initialPlacement":H=h;break}if(o!==H)return{reset:{placement:H}}}return{}}}};async function Ux(l,a){const{placement:r,platform:s,elements:o}=l,f=await(s.isRTL==null?void 0:s.isRTL(o.floating)),d=Ma(r),h=Qr(r),p=Ai(r)==="y",m=["left","top"].includes(d)?-1:1,g=f&&p?-1:1,S=Zr(a,l);let{mainAxis:x,crossAxis:R,alignmentAxis:E}=typeof S=="number"?{mainAxis:S,crossAxis:0,alignmentAxis:null}:{mainAxis:S.mainAxis||0,crossAxis:S.crossAxis||0,alignmentAxis:S.alignmentAxis};return h&&typeof E=="number"&&(R=h==="end"?E*-1:E),p?{x:R*g,y:x*m}:{x:x*m,y:R*g}}const zx=function(l){return l===void 0&&(l=0),{name:"offset",options:l,async fn(a){var r,s;const{x:o,y:f,placement:d,middlewareData:h}=a,p=await Ux(a,l);return d===((r=h.offset)==null?void 0:r.placement)&&(s=h.arrow)!=null&&s.alignmentOffset?{}:{x:o+p.x,y:f+p.y,data:{...p,placement:d}}}}},Lx=function(l){return l===void 0&&(l={}),{name:"shift",options:l,async fn(a){const{x:r,y:s,placement:o}=a,{mainAxis:f=!0,crossAxis:d=!1,limiter:h={fn:D=>{let{x:w,y:T}=D;return{x:w,y:T}}},...p}=Zr(l,a),m={x:r,y:s},g=await Ly(a,p),S=Ai(Ma(o)),x=Uy(S);let R=m[x],E=m[S];if(f){const D=x==="y"?"top":"left",w=x==="y"?"bottom":"right",T=R+g[D],M=R-g[w];R=jf(T,R,M)}if(d){const D=S==="y"?"top":"left",w=S==="y"?"bottom":"right",T=E+g[D],M=E-g[w];E=jf(T,E,M)}const C=h.fn({...a,[x]:R,[S]:E});return{...C,data:{x:C.x-r,y:C.y-s,enabled:{[x]:f,[S]:d}}}}}};function Lu(){return typeof window<"u"}function Ci(l){return By(l)?(l.nodeName||"").toLowerCase():"#document"}function hn(l){var a;return(l==null||(a=l.ownerDocument)==null?void 0:a.defaultView)||window}function ul(l){var a;return(a=(By(l)?l.ownerDocument:l.document)||window.document)==null?void 0:a.documentElement}function By(l){return Lu()?l instanceof Node||l instanceof hn(l).Node:!1}function Bn(l){return Lu()?l instanceof Element||l instanceof hn(l).Element:!1}function rl(l){return Lu()?l instanceof HTMLElement||l instanceof hn(l).HTMLElement:!1}function D0(l){return!Lu()||typeof ShadowRoot>"u"?!1:l instanceof ShadowRoot||l instanceof hn(l).ShadowRoot}function Kr(l){const{overflow:a,overflowX:r,overflowY:s,display:o}=kn(l);return/auto|scroll|overlay|hidden|clip/.test(a+s+r)&&!["inline","contents"].includes(o)}function Bx(l){return["table","td","th"].includes(Ci(l))}function Bu(l){return[":popover-open",":modal"].some(a=>{try{return l.matches(a)}catch{return!1}})}function ad(l){const a=id(),r=Bn(l)?kn(l):l;return["transform","translate","scale","rotate","perspective"].some(s=>r[s]?r[s]!=="none":!1)||(r.containerType?r.containerType!=="normal":!1)||!a&&(r.backdropFilter?r.backdropFilter!=="none":!1)||!a&&(r.filter?r.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(s=>(r.willChange||"").includes(s))||["paint","layout","strict","content"].some(s=>(r.contain||"").includes(s))}function kx(l){let a=ua(l);for(;rl(a)&&!_i(a);){if(ad(a))return a;if(Bu(a))return null;a=ua(a)}return null}function id(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function _i(l){return["html","body","#document"].includes(Ci(l))}function kn(l){return hn(l).getComputedStyle(l)}function ku(l){return Bn(l)?{scrollLeft:l.scrollLeft,scrollTop:l.scrollTop}:{scrollLeft:l.scrollX,scrollTop:l.scrollY}}function ua(l){if(Ci(l)==="html")return l;const a=l.assignedSlot||l.parentNode||D0(l)&&l.host||ul(l);return D0(a)?a.host:a}function ky(l){const a=ua(l);return _i(a)?l.ownerDocument?l.ownerDocument.body:l.body:rl(a)&&Kr(a)?a:ky(a)}function qr(l,a,r){var s;a===void 0&&(a=[]),r===void 0&&(r=!0);const o=ky(l),f=o===((s=l.ownerDocument)==null?void 0:s.body),d=hn(o);if(f){const h=Cf(d);return a.concat(d,d.visualViewport||[],Kr(o)?o:[],h&&r?qr(h):[])}return a.concat(o,qr(o,[],r))}function Cf(l){return l.parent&&Object.getPrototypeOf(l.parent)?l.frameElement:null}function Hy(l){const a=kn(l);let r=parseFloat(a.width)||0,s=parseFloat(a.height)||0;const o=rl(l),f=o?l.offsetWidth:r,d=o?l.offsetHeight:s,h=Tu(r)!==f||Tu(s)!==d;return h&&(r=f,s=d),{width:r,height:s,$:h}}function rd(l){return Bn(l)?l:l.contextElement}function Ei(l){const a=rd(l);if(!rl(a))return al(1);const r=a.getBoundingClientRect(),{width:s,height:o,$:f}=Hy(a);let d=(f?Tu(r.width):r.width)/s,h=(f?Tu(r.height):r.height)/o;return(!d||!Number.isFinite(d))&&(d=1),(!h||!Number.isFinite(h))&&(h=1),{x:d,y:h}}const Hx=al(0);function qy(l){const a=hn(l);return!id()||!a.visualViewport?Hx:{x:a.visualViewport.offsetLeft,y:a.visualViewport.offsetTop}}function qx(l,a,r){return a===void 0&&(a=!1),!r||a&&r!==hn(l)?!1:a}function Ua(l,a,r,s){a===void 0&&(a=!1),r===void 0&&(r=!1);const o=l.getBoundingClientRect(),f=rd(l);let d=al(1);a&&(s?Bn(s)&&(d=Ei(s)):d=Ei(l));const h=qx(f,r,s)?qy(f):al(0);let p=(o.left+h.x)/d.x,m=(o.top+h.y)/d.y,g=o.width/d.x,S=o.height/d.y;if(f){const x=hn(f),R=s&&Bn(s)?hn(s):s;let E=x,C=Cf(E);for(;C&&s&&R!==E;){const D=Ei(C),w=C.getBoundingClientRect(),T=kn(C),M=w.left+(C.clientLeft+parseFloat(T.paddingLeft))*D.x,X=w.top+(C.clientTop+parseFloat(T.paddingTop))*D.y;p*=D.x,m*=D.y,g*=D.x,S*=D.y,p+=M,m+=X,E=hn(C),C=Cf(E)}}return Ou({width:g,height:S,x:p,y:m})}function sd(l,a){const r=ku(l).scrollLeft;return a?a.left+r:Ua(ul(l)).left+r}function Vy(l,a,r){r===void 0&&(r=!1);const s=l.getBoundingClientRect(),o=s.left+a.scrollLeft-(r?0:sd(l,s)),f=s.top+a.scrollTop;return{x:o,y:f}}function Vx(l){let{elements:a,rect:r,offsetParent:s,strategy:o}=l;const f=o==="fixed",d=ul(s),h=a?Bu(a.floating):!1;if(s===d||h&&f)return r;let p={scrollLeft:0,scrollTop:0},m=al(1);const g=al(0),S=rl(s);if((S||!S&&!f)&&((Ci(s)!=="body"||Kr(d))&&(p=ku(s)),rl(s))){const R=Ua(s);m=Ei(s),g.x=R.x+s.clientLeft,g.y=R.y+s.clientTop}const x=d&&!S&&!f?Vy(d,p,!0):al(0);return{width:r.width*m.x,height:r.height*m.y,x:r.x*m.x-p.scrollLeft*m.x+g.x+x.x,y:r.y*m.y-p.scrollTop*m.y+g.y+x.y}}function Fx(l){return Array.from(l.getClientRects())}function Yx(l){const a=ul(l),r=ku(l),s=l.ownerDocument.body,o=Ca(a.scrollWidth,a.clientWidth,s.scrollWidth,s.clientWidth),f=Ca(a.scrollHeight,a.clientHeight,s.scrollHeight,s.clientHeight);let d=-r.scrollLeft+sd(l);const h=-r.scrollTop;return kn(s).direction==="rtl"&&(d+=Ca(a.clientWidth,s.clientWidth)-o),{width:o,height:f,x:d,y:h}}function Gx(l,a){const r=hn(l),s=ul(l),o=r.visualViewport;let f=s.clientWidth,d=s.clientHeight,h=0,p=0;if(o){f=o.width,d=o.height;const m=id();(!m||m&&a==="fixed")&&(h=o.offsetLeft,p=o.offsetTop)}return{width:f,height:d,x:h,y:p}}function $x(l,a){const r=Ua(l,!0,a==="fixed"),s=r.top+l.clientTop,o=r.left+l.clientLeft,f=rl(l)?Ei(l):al(1),d=l.clientWidth*f.x,h=l.clientHeight*f.y,p=o*f.x,m=s*f.y;return{width:d,height:h,x:p,y:m}}function C0(l,a,r){let s;if(a==="viewport")s=Gx(l,r);else if(a==="document")s=Yx(ul(l));else if(Bn(a))s=$x(a,r);else{const o=qy(l);s={x:a.x-o.x,y:a.y-o.y,width:a.width,height:a.height}}return Ou(s)}function Fy(l,a){const r=ua(l);return r===a||!Bn(r)||_i(r)?!1:kn(r).position==="fixed"||Fy(r,a)}function Xx(l,a){const r=a.get(l);if(r)return r;let s=qr(l,[],!1).filter(h=>Bn(h)&&Ci(h)!=="body"),o=null;const f=kn(l).position==="fixed";let d=f?ua(l):l;for(;Bn(d)&&!_i(d);){const h=kn(d),p=ad(d);!p&&h.position==="fixed"&&(o=null),(f?!p&&!o:!p&&h.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Kr(d)&&!p&&Fy(l,d))?s=s.filter(g=>g!==d):o=h,d=ua(d)}return a.set(l,s),s}function Zx(l){let{element:a,boundary:r,rootBoundary:s,strategy:o}=l;const d=[...r==="clippingAncestors"?Bu(a)?[]:Xx(a,this._c):[].concat(r),s],h=d[0],p=d.reduce((m,g)=>{const S=C0(a,g,o);return m.top=Ca(S.top,m.top),m.right=wi(S.right,m.right),m.bottom=wi(S.bottom,m.bottom),m.left=Ca(S.left,m.left),m},C0(a,h,o));return{width:p.right-p.left,height:p.bottom-p.top,x:p.left,y:p.top}}function Qx(l){const{width:a,height:r}=Hy(l);return{width:a,height:r}}function Kx(l,a,r){const s=rl(a),o=ul(a),f=r==="fixed",d=Ua(l,!0,f,a);let h={scrollLeft:0,scrollTop:0};const p=al(0);if(s||!s&&!f)if((Ci(a)!=="body"||Kr(o))&&(h=ku(a)),s){const x=Ua(a,!0,f,a);p.x=x.x+a.clientLeft,p.y=x.y+a.clientTop}else o&&(p.x=sd(o));const m=o&&!s&&!f?Vy(o,h):al(0),g=d.left+h.scrollLeft-p.x-m.x,S=d.top+h.scrollTop-p.y-m.y;return{x:g,y:S,width:d.width,height:d.height}}function pf(l){return kn(l).position==="static"}function N0(l,a){if(!rl(l)||kn(l).position==="fixed")return null;if(a)return a(l);let r=l.offsetParent;return ul(l)===r&&(r=r.ownerDocument.body),r}function Yy(l,a){const r=hn(l);if(Bu(l))return r;if(!rl(l)){let o=ua(l);for(;o&&!_i(o);){if(Bn(o)&&!pf(o))return o;o=ua(o)}return r}let s=N0(l,a);for(;s&&Bx(s)&&pf(s);)s=N0(s,a);return s&&_i(s)&&pf(s)&&!ad(s)?r:s||kx(l)||r}const Jx=async function(l){const a=this.getOffsetParent||Yy,r=this.getDimensions,s=await r(l.floating);return{reference:Kx(l.reference,await a(l.floating),l.strategy),floating:{x:0,y:0,width:s.width,height:s.height}}};function Px(l){return kn(l).direction==="rtl"}const Wx={convertOffsetParentRelativeRectToViewportRelativeRect:Vx,getDocumentElement:ul,getClippingRect:Zx,getOffsetParent:Yy,getElementRects:Jx,getClientRects:Fx,getDimensions:Qx,getScale:Ei,isElement:Bn,isRTL:Px};function Gy(l,a){return l.x===a.x&&l.y===a.y&&l.width===a.width&&l.height===a.height}function Ix(l,a){let r=null,s;const o=ul(l);function f(){var h;clearTimeout(s),(h=r)==null||h.disconnect(),r=null}function d(h,p){h===void 0&&(h=!1),p===void 0&&(p=1),f();const m=l.getBoundingClientRect(),{left:g,top:S,width:x,height:R}=m;if(h||a(),!x||!R)return;const E=cu(S),C=cu(o.clientWidth-(g+x)),D=cu(o.clientHeight-(S+R)),w=cu(g),M={rootMargin:-E+"px "+-C+"px "+-D+"px "+-w+"px",threshold:Ca(0,wi(1,p))||1};let X=!0;function V(I){const W=I[0].intersectionRatio;if(W!==p){if(!X)return d();W?d(!1,W):s=setTimeout(()=>{d(!1,1e-7)},1e3)}W===1&&!Gy(m,l.getBoundingClientRect())&&d(),X=!1}try{r=new IntersectionObserver(V,{...M,root:o.ownerDocument})}catch{r=new IntersectionObserver(V,M)}r.observe(l)}return d(!0),f}function eE(l,a,r,s){s===void 0&&(s={});const{ancestorScroll:o=!0,ancestorResize:f=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:p=!1}=s,m=rd(l),g=o||f?[...m?qr(m):[],...qr(a)]:[];g.forEach(w=>{o&&w.addEventListener("scroll",r,{passive:!0}),f&&w.addEventListener("resize",r)});const S=m&&h?Ix(m,r):null;let x=-1,R=null;d&&(R=new ResizeObserver(w=>{let[T]=w;T&&T.target===m&&R&&(R.unobserve(a),cancelAnimationFrame(x),x=requestAnimationFrame(()=>{var M;(M=R)==null||M.observe(a)})),r()}),m&&!p&&R.observe(m),R.observe(a));let E,C=p?Ua(l):null;p&&D();function D(){const w=Ua(l);C&&!Gy(C,w)&&r(),C=w,E=requestAnimationFrame(D)}return r(),()=>{var w;g.forEach(T=>{o&&T.removeEventListener("scroll",r),f&&T.removeEventListener("resize",r)}),S==null||S(),(w=R)==null||w.disconnect(),R=null,p&&cancelAnimationFrame(E)}}const tE=zx,nE=Lx,lE=Mx,aE=Nx,M0=(l,a,r)=>{const s=new Map,o={platform:Wx,...r},f={...o.platform,_c:s};return Cx(l,a,{...o,platform:f})};var yf={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var U0;function iE(){return U0||(U0=1,function(l){(function(){var a={}.hasOwnProperty;function r(){for(var f="",d=0;d<arguments.length;d++){var h=arguments[d];h&&(f=o(f,s(h)))}return f}function s(f){if(typeof f=="string"||typeof f=="number")return f;if(typeof f!="object")return"";if(Array.isArray(f))return r.apply(null,f);if(f.toString!==Object.prototype.toString&&!f.toString.toString().includes("[native code]"))return f.toString();var d="";for(var h in f)a.call(f,h)&&f[h]&&(d=o(d,h));return d}function o(f,d){return d?f?f+" "+d:f+d:f}l.exports?(r.default=r,l.exports=r):window.classNames=r})()}(yf)),yf.exports}var rE=iE();const Nf=qf(rE);var z0={};const sE="react-tooltip-core-styles",uE="react-tooltip-base-styles",L0={core:!1,base:!1};function B0({css:l,id:a=uE,type:r="base",ref:s}){var o,f;if(!l||typeof document>"u"||L0[r]||r==="core"&&typeof process<"u"&&(!((o=process==null?void 0:z0)===null||o===void 0)&&o.REACT_TOOLTIP_DISABLE_CORE_STYLES)||r!=="base"&&typeof process<"u"&&(!((f=process==null?void 0:z0)===null||f===void 0)&&f.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;r==="core"&&(a=sE),s||(s={});const{insertAt:d}=s;if(document.getElementById(a))return;const h=document.head||document.getElementsByTagName("head")[0],p=document.createElement("style");p.id=a,p.type="text/css",d==="top"&&h.firstChild?h.insertBefore(p,h.firstChild):h.appendChild(p),p.styleSheet?p.styleSheet.cssText=l:p.appendChild(document.createTextNode(l)),L0[r]=!0}const k0=async({elementReference:l=null,tooltipReference:a=null,tooltipArrowReference:r=null,place:s="top",offset:o=10,strategy:f="absolute",middlewares:d=[tE(Number(o)),lE({fallbackAxisSideDirection:"start"}),nE({padding:5})],border:h})=>{if(!l)return{tooltipStyles:{},tooltipArrowStyles:{},place:s};if(a===null)return{tooltipStyles:{},tooltipArrowStyles:{},place:s};const p=d;return r?(p.push(aE({element:r,padding:5})),M0(l,a,{placement:s,strategy:f,middleware:p}).then(({x:m,y:g,placement:S,middlewareData:x})=>{var R,E;const C={left:`${m}px`,top:`${g}px`,border:h},{x:D,y:w}=(R=x.arrow)!==null&&R!==void 0?R:{x:0,y:0},T=(E={top:"bottom",right:"left",bottom:"top",left:"right"}[S.split("-")[0]])!==null&&E!==void 0?E:"bottom",M=h&&{borderBottom:h,borderRight:h};let X=0;if(h){const V=`${h}`.match(/(\d+)px/);X=V!=null&&V[1]?Number(V[1]):1}return{tooltipStyles:C,tooltipArrowStyles:{left:D!=null?`${D}px`:"",top:w!=null?`${w}px`:"",right:"",bottom:"",...M,[T]:`-${4+X}px`},place:S}})):M0(l,a,{placement:"bottom",strategy:f,middleware:p}).then(({x:m,y:g,placement:S})=>({tooltipStyles:{left:`${m}px`,top:`${g}px`},tooltipArrowStyles:{},place:S}))},H0=(l,a)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(l,a),q0=(l,a,r)=>{let s=null;const o=function(...f){const d=()=>{s=null};!s&&(l.apply(this,f),s=setTimeout(d,a))};return o.cancel=()=>{s&&(clearTimeout(s),s=null)},o},V0=l=>l!==null&&!Array.isArray(l)&&typeof l=="object",Mf=(l,a)=>{if(l===a)return!0;if(Array.isArray(l)&&Array.isArray(a))return l.length===a.length&&l.every((o,f)=>Mf(o,a[f]));if(Array.isArray(l)!==Array.isArray(a))return!1;if(!V0(l)||!V0(a))return l===a;const r=Object.keys(l),s=Object.keys(a);return r.length===s.length&&r.every(o=>Mf(l[o],a[o]))},oE=l=>{if(!(l instanceof HTMLElement||l instanceof SVGElement))return!1;const a=getComputedStyle(l);return["overflow","overflow-x","overflow-y"].some(r=>{const s=a.getPropertyValue(r);return s==="auto"||s==="scroll"})},F0=l=>{if(!l)return null;let a=l.parentElement;for(;a;){if(oE(a))return a;a=a.parentElement}return document.scrollingElement||document.documentElement},cE=typeof window<"u"?_.useLayoutEffect:_.useEffect,Tn=l=>{l.current&&(clearTimeout(l.current),l.current=null)},fE="DEFAULT_TOOLTIP_ID",dE={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},hE=_.createContext({getTooltipData:()=>dE});function $y(l=fE){return _.useContext(hE).getTooltipData(l)}var Si={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},gf={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const mE=({forwardRef:l,id:a,className:r,classNameArrow:s,variant:o="dark",anchorId:f,anchorSelect:d,place:h="top",offset:p=10,events:m=["hover"],openOnClick:g=!1,positionStrategy:S="absolute",middlewares:x,wrapper:R,delayShow:E=0,delayHide:C=0,float:D=!1,hidden:w=!1,noArrow:T=!1,clickable:M=!1,closeOnEsc:X=!1,closeOnScroll:V=!1,closeOnResize:I=!1,openEvents:W,closeEvents:se,globalCloseEvents:fe,imperativeModeOnly:De,style:ge,position:re,afterShow:ye,afterHide:de,disableTooltip:_e,content:H,contentWrapperRef:J,isOpen:te,defaultIsOpen:we=!1,setIsOpen:O,activeAnchor:Y,setActiveAnchor:ae,border:ee,opacity:he,arrowColor:je,role:xe="tooltip"})=>{var at;const Te=_.useRef(null),bt=_.useRef(null),wt=_.useRef(null),jt=_.useRef(null),mn=_.useRef(null),[Gt,Yn]=_.useState({tooltipStyles:{},tooltipArrowStyles:{},place:h}),[Dt,ol]=_.useState(!1),[st,Rn]=_.useState(!1),[Ye,cl]=_.useState(null),nn=_.useRef(!1),j=_.useRef(null),{anchorRefs:B,setActiveAnchor:G}=$y(a),ie=_.useRef(!1),[P,K]=_.useState([]),ne=_.useRef(!1),ve=g||m.includes("click"),Pe=ve||(W==null?void 0:W.click)||(W==null?void 0:W.dblclick)||(W==null?void 0:W.mousedown),et=W?{...W}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!W&&ve&&Object.assign(et,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const On=se?{...se}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!se&&ve&&Object.assign(On,{mouseleave:!1,blur:!1,mouseout:!1});const mt=fe?{...fe}:{escape:X||!1,scroll:V||!1,resize:I||!1,clickOutsideAnchor:Pe||!1};De&&(Object.assign(et,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(On,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(mt,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),cE(()=>(ne.current=!0,()=>{ne.current=!1}),[]);const qe=le=>{ne.current&&(le&&Rn(!0),setTimeout(()=>{ne.current&&(O==null||O(le),te===void 0&&ol(le))},10))};_.useEffect(()=>{if(te===void 0)return()=>null;te&&Rn(!0);const le=setTimeout(()=>{ol(te)},10);return()=>{clearTimeout(le)}},[te]),_.useEffect(()=>{if(Dt!==nn.current)if(Tn(mn),nn.current=Dt,Dt)ye==null||ye();else{const le=(ue=>{const me=ue.match(/^([\d.]+)(ms|s)$/);if(!me)return 0;const[,We,pt]=me;return Number(We)*(pt==="ms"?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));mn.current=setTimeout(()=>{Rn(!1),cl(null),de==null||de()},le+25)}},[Dt]);const $t=le=>{Yn(ue=>Mf(ue,le)?ue:le)},ln=(le=E)=>{Tn(wt),st?qe(!0):wt.current=setTimeout(()=>{qe(!0)},le)},Xt=(le=C)=>{Tn(jt),jt.current=setTimeout(()=>{ie.current||qe(!1)},le)},Gn=le=>{var ue;if(!le)return;const me=(ue=le.currentTarget)!==null&&ue!==void 0?ue:le.target;if(!(me!=null&&me.isConnected))return ae(null),void G({current:null});E?ln():qe(!0),ae(me),G({current:me}),Tn(jt)},fl=()=>{M?Xt(C||100):C?Xt():qe(!1),Tn(wt)},dl=({x:le,y:ue})=>{var me;const We={getBoundingClientRect:()=>({x:le,y:ue,width:0,height:0,top:ue,left:le,right:le,bottom:ue})};k0({place:(me=Ye==null?void 0:Ye.place)!==null&&me!==void 0?me:h,offset:p,elementReference:We,tooltipReference:Te.current,tooltipArrowReference:bt.current,strategy:S,middlewares:x,border:ee}).then(pt=>{$t(pt)})},$n=le=>{if(!le)return;const ue=le,me={x:ue.clientX,y:ue.clientY};dl(me),j.current=me},jn=le=>{var ue;if(!Dt)return;const me=le.target;me.isConnected&&(!((ue=Te.current)===null||ue===void 0)&&ue.contains(me)||[document.querySelector(`[id='${f}']`),...P].some(We=>We==null?void 0:We.contains(me))||(qe(!1),Tn(wt)))},La=q0(Gn,50),ut=q0(fl,50),Ht=le=>{ut.cancel(),La(le)},be=()=>{La.cancel(),ut()},Ne=_.useCallback(()=>{var le,ue;const me=(le=Ye==null?void 0:Ye.position)!==null&&le!==void 0?le:re;me?dl(me):D?j.current&&dl(j.current):Y!=null&&Y.isConnected&&k0({place:(ue=Ye==null?void 0:Ye.place)!==null&&ue!==void 0?ue:h,offset:p,elementReference:Y,tooltipReference:Te.current,tooltipArrowReference:bt.current,strategy:S,middlewares:x,border:ee}).then(We=>{ne.current&&$t(We)})},[Dt,Y,H,ge,h,Ye==null?void 0:Ye.place,p,S,re,Ye==null?void 0:Ye.position,D]);_.useEffect(()=>{var le,ue;const me=new Set(B);P.forEach(Le=>{_e!=null&&_e(Le)||me.add({current:Le})});const We=document.querySelector(`[id='${f}']`);We&&!(_e!=null&&_e(We))&&me.add({current:We});const pt=()=>{qe(!1)},pn=F0(Y),yn=F0(Te.current);mt.scroll&&(window.addEventListener("scroll",pt),pn==null||pn.addEventListener("scroll",pt),yn==null||yn.addEventListener("scroll",pt));let At=null;mt.resize?window.addEventListener("resize",pt):Y&&Te.current&&(At=eE(Y,Te.current,Ne,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const _t=Le=>{Le.key==="Escape"&&qe(!1)};mt.escape&&window.addEventListener("keydown",_t),mt.clickOutsideAnchor&&window.addEventListener("click",jn);const Ge=[],an=Le=>!!(Le!=null&&Le.target&&(Y!=null&&Y.contains(Le.target))),zl=Le=>{Dt&&an(Le)||Gn(Le)},ca=Le=>{Dt&&an(Le)&&fl()},ml=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],ot=["click","dblclick","mousedown","mouseup"];Object.entries(et).forEach(([Le,Dn])=>{Dn&&(ml.includes(Le)?Ge.push({event:Le,listener:Ht}):ot.includes(Le)&&Ge.push({event:Le,listener:zl}))}),Object.entries(On).forEach(([Le,Dn])=>{Dn&&(ml.includes(Le)?Ge.push({event:Le,listener:be}):ot.includes(Le)&&Ge.push({event:Le,listener:ca}))}),D&&Ge.push({event:"pointermove",listener:$n});const Ui=()=>{ie.current=!0},zi=()=>{ie.current=!1,fl()},Xn=M&&(On.mouseout||On.mouseleave);return Xn&&((le=Te.current)===null||le===void 0||le.addEventListener("mouseover",Ui),(ue=Te.current)===null||ue===void 0||ue.addEventListener("mouseout",zi)),Ge.forEach(({event:Le,listener:Dn})=>{me.forEach(Ba=>{var Ll;(Ll=Ba.current)===null||Ll===void 0||Ll.addEventListener(Le,Dn)})}),()=>{var Le,Dn;mt.scroll&&(window.removeEventListener("scroll",pt),pn==null||pn.removeEventListener("scroll",pt),yn==null||yn.removeEventListener("scroll",pt)),mt.resize?window.removeEventListener("resize",pt):At==null||At(),mt.clickOutsideAnchor&&window.removeEventListener("click",jn),mt.escape&&window.removeEventListener("keydown",_t),Xn&&((Le=Te.current)===null||Le===void 0||Le.removeEventListener("mouseover",Ui),(Dn=Te.current)===null||Dn===void 0||Dn.removeEventListener("mouseout",zi)),Ge.forEach(({event:Ba,listener:Ll})=>{me.forEach(Xu=>{var Bl;(Bl=Xu.current)===null||Bl===void 0||Bl.removeEventListener(Ba,Ll)})})}},[Y,Ne,st,B,P,W,se,fe,ve,E,C]),_.useEffect(()=>{var le,ue;let me=(ue=(le=Ye==null?void 0:Ye.anchorSelect)!==null&&le!==void 0?le:d)!==null&&ue!==void 0?ue:"";!me&&a&&(me=`[data-tooltip-id='${a.replace(/'/g,"\\'")}']`);const We=new MutationObserver(pt=>{const pn=[],yn=[];pt.forEach(At=>{if(At.type==="attributes"&&At.attributeName==="data-tooltip-id"&&(At.target.getAttribute("data-tooltip-id")===a?pn.push(At.target):At.oldValue===a&&yn.push(At.target)),At.type==="childList"){if(Y){const _t=[...At.removedNodes].filter(Ge=>Ge.nodeType===1);if(me)try{yn.push(..._t.filter(Ge=>Ge.matches(me))),yn.push(..._t.flatMap(Ge=>[...Ge.querySelectorAll(me)]))}catch{}_t.some(Ge=>{var an;return!!(!((an=Ge==null?void 0:Ge.contains)===null||an===void 0)&&an.call(Ge,Y))&&(Rn(!1),qe(!1),ae(null),Tn(wt),Tn(jt),!0)})}if(me)try{const _t=[...At.addedNodes].filter(Ge=>Ge.nodeType===1);pn.push(..._t.filter(Ge=>Ge.matches(me))),pn.push(..._t.flatMap(Ge=>[...Ge.querySelectorAll(me)]))}catch{}}}),(pn.length||yn.length)&&K(At=>[...At.filter(_t=>!yn.includes(_t)),...pn])});return We.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{We.disconnect()}},[a,d,Ye==null?void 0:Ye.anchorSelect,Y]),_.useEffect(()=>{Ne()},[Ne]),_.useEffect(()=>{if(!(J!=null&&J.current))return()=>null;const le=new ResizeObserver(()=>{setTimeout(()=>Ne())});return le.observe(J.current),()=>{le.disconnect()}},[H,J==null?void 0:J.current]),_.useEffect(()=>{var le;const ue=document.querySelector(`[id='${f}']`),me=[...P,ue];Y&&me.includes(Y)||ae((le=P[0])!==null&&le!==void 0?le:ue)},[f,P,Y]),_.useEffect(()=>(we&&qe(!0),()=>{Tn(wt),Tn(jt)}),[]),_.useEffect(()=>{var le;let ue=(le=Ye==null?void 0:Ye.anchorSelect)!==null&&le!==void 0?le:d;if(!ue&&a&&(ue=`[data-tooltip-id='${a.replace(/'/g,"\\'")}']`),ue)try{const me=Array.from(document.querySelectorAll(ue));K(me)}catch{K([])}},[a,d,Ye==null?void 0:Ye.anchorSelect]),_.useEffect(()=>{wt.current&&(Tn(wt),ln(E))},[E]);const Ut=(at=Ye==null?void 0:Ye.content)!==null&&at!==void 0?at:H,hl=Dt&&Object.keys(Gt.tooltipStyles).length>0;return _.useImperativeHandle(l,()=>({open:le=>{if(le!=null&&le.anchorSelect)try{document.querySelector(le.anchorSelect)}catch{return void console.warn(`[react-tooltip] "${le.anchorSelect}" is not a valid CSS selector`)}cl(le??null),le!=null&&le.delay?ln(le.delay):qe(!0)},close:le=>{le!=null&&le.delay?Xt(le.delay):qe(!1)},activeAnchor:Y,place:Gt.place,isOpen:!!(st&&!w&&Ut&&hl)})),st&&!w&&Ut?Je.createElement(R,{id:a,role:xe,className:Nf("react-tooltip",Si.tooltip,gf.tooltip,gf[o],r,`react-tooltip__place-${Gt.place}`,Si[hl?"show":"closing"],hl?"react-tooltip__show":"react-tooltip__closing",S==="fixed"&&Si.fixed,M&&Si.clickable),onTransitionEnd:le=>{Tn(mn),Dt||le.propertyName!=="opacity"||(Rn(!1),cl(null),de==null||de())},style:{...ge,...Gt.tooltipStyles,opacity:he!==void 0&&hl?he:void 0},ref:Te},Ut,Je.createElement(R,{className:Nf("react-tooltip-arrow",Si.arrow,gf.arrow,s,T&&Si.noArrow),style:{...Gt.tooltipArrowStyles,background:je?`linear-gradient(to right bottom, transparent 50%, ${je} 50%)`:void 0},ref:bt})):null},pE=({content:l})=>Je.createElement("span",{dangerouslySetInnerHTML:{__html:l}}),Xy=Je.forwardRef(({id:l,anchorId:a,anchorSelect:r,content:s,html:o,render:f,className:d,classNameArrow:h,variant:p="dark",place:m="top",offset:g=10,wrapper:S="div",children:x=null,events:R=["hover"],openOnClick:E=!1,positionStrategy:C="absolute",middlewares:D,delayShow:w=0,delayHide:T=0,float:M=!1,hidden:X=!1,noArrow:V=!1,clickable:I=!1,closeOnEsc:W=!1,closeOnScroll:se=!1,closeOnResize:fe=!1,openEvents:De,closeEvents:ge,globalCloseEvents:re,imperativeModeOnly:ye=!1,style:de,position:_e,isOpen:H,defaultIsOpen:J=!1,disableStyleInjection:te=!1,border:we,opacity:O,arrowColor:Y,setIsOpen:ae,afterShow:ee,afterHide:he,disableTooltip:je,role:xe="tooltip"},at)=>{const[Te,bt]=_.useState(s),[wt,jt]=_.useState(o),[mn,Gt]=_.useState(m),[Yn,Dt]=_.useState(p),[ol,st]=_.useState(g),[Rn,Ye]=_.useState(w),[cl,nn]=_.useState(T),[j,B]=_.useState(M),[G,ie]=_.useState(X),[P,K]=_.useState(S),[ne,ve]=_.useState(R),[Pe,et]=_.useState(C),[On,mt]=_.useState(null),[qe,$t]=_.useState(null),ln=_.useRef(te),{anchorRefs:Xt,activeAnchor:Gn}=$y(l),fl=ut=>ut==null?void 0:ut.getAttributeNames().reduce((Ht,be)=>{var Ne;return be.startsWith("data-tooltip-")&&(Ht[be.replace(/^data-tooltip-/,"")]=(Ne=ut==null?void 0:ut.getAttribute(be))!==null&&Ne!==void 0?Ne:null),Ht},{}),dl=ut=>{const Ht={place:be=>{var Ne;Gt((Ne=be)!==null&&Ne!==void 0?Ne:m)},content:be=>{bt(be??s)},html:be=>{jt(be??o)},variant:be=>{var Ne;Dt((Ne=be)!==null&&Ne!==void 0?Ne:p)},offset:be=>{st(be===null?g:Number(be))},wrapper:be=>{var Ne;K((Ne=be)!==null&&Ne!==void 0?Ne:S)},events:be=>{const Ne=be==null?void 0:be.split(" ");ve(Ne??R)},"position-strategy":be=>{var Ne;et((Ne=be)!==null&&Ne!==void 0?Ne:C)},"delay-show":be=>{Ye(be===null?w:Number(be))},"delay-hide":be=>{nn(be===null?T:Number(be))},float:be=>{B(be===null?M:be==="true")},hidden:be=>{ie(be===null?X:be==="true")},"class-name":be=>{mt(be)}};Object.values(Ht).forEach(be=>be(null)),Object.entries(ut).forEach(([be,Ne])=>{var Ut;(Ut=Ht[be])===null||Ut===void 0||Ut.call(Ht,Ne)})};_.useEffect(()=>{bt(s)},[s]),_.useEffect(()=>{jt(o)},[o]),_.useEffect(()=>{Gt(m)},[m]),_.useEffect(()=>{Dt(p)},[p]),_.useEffect(()=>{st(g)},[g]),_.useEffect(()=>{Ye(w)},[w]),_.useEffect(()=>{nn(T)},[T]),_.useEffect(()=>{B(M)},[M]),_.useEffect(()=>{ie(X)},[X]),_.useEffect(()=>{et(C)},[C]),_.useEffect(()=>{ln.current!==te&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[te]),_.useEffect(()=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:te==="core",disableBase:te}}))},[]),_.useEffect(()=>{var ut;const Ht=new Set(Xt);let be=r;if(!be&&l&&(be=`[data-tooltip-id='${l.replace(/'/g,"\\'")}']`),be)try{document.querySelectorAll(be).forEach(ue=>{Ht.add({current:ue})})}catch{console.warn(`[react-tooltip] "${be}" is not a valid CSS selector`)}const Ne=document.querySelector(`[id='${a}']`);if(Ne&&Ht.add({current:Ne}),!Ht.size)return()=>null;const Ut=(ut=qe??Ne)!==null&&ut!==void 0?ut:Gn.current,hl=new MutationObserver(ue=>{ue.forEach(me=>{var We;if(!Ut||me.type!=="attributes"||!(!((We=me.attributeName)===null||We===void 0)&&We.startsWith("data-tooltip-")))return;const pt=fl(Ut);dl(pt)})}),le={attributes:!0,childList:!1,subtree:!1};if(Ut){const ue=fl(Ut);dl(ue),hl.observe(Ut,le)}return()=>{hl.disconnect()}},[Xt,Gn,qe,a,r]),_.useEffect(()=>{de!=null&&de.border&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),we&&!H0("border",`${we}`)&&console.warn(`[react-tooltip] "${we}" is not a valid \`border\`.`),de!=null&&de.opacity&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),O&&!H0("opacity",`${O}`)&&console.warn(`[react-tooltip] "${O}" is not a valid \`opacity\`.`)},[]);let $n=x;const jn=_.useRef(null);if(f){const ut=f({content:(qe==null?void 0:qe.getAttribute("data-tooltip-content"))||Te||null,activeAnchor:qe});$n=ut?Je.createElement("div",{ref:jn,className:"react-tooltip-content-wrapper"},ut):null}else Te&&($n=Te);wt&&($n=Je.createElement(pE,{content:wt}));const La={forwardRef:at,id:l,anchorId:a,anchorSelect:r,className:Nf(d,On),classNameArrow:h,content:$n,contentWrapperRef:jn,place:mn,variant:Yn,offset:ol,wrapper:P,events:ne,openOnClick:E,positionStrategy:Pe,middlewares:D,delayShow:Rn,delayHide:cl,float:j,hidden:G,noArrow:V,clickable:I,closeOnEsc:W,closeOnScroll:se,closeOnResize:fe,openEvents:De,closeEvents:ge,globalCloseEvents:re,imperativeModeOnly:ye,style:de,position:_e,isOpen:H,defaultIsOpen:J,border:we,opacity:O,arrowColor:Y,setIsOpen:ae,afterShow:ee,afterHide:he,disableTooltip:je,activeAnchor:qe,setActiveAnchor:ut=>$t(ut),role:xe};return Je.createElement(mE,{...La})});typeof window<"u"&&window.addEventListener("react-tooltip-inject-styles",l=>{l.detail.disableCore||B0({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),l.detail.disableBase||B0({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});function ud({showModal:l}){const a=Vn(),r=sl(),[s,o]=_.useState(sessionStorage.getItem("current_user")?JSON.parse(sessionStorage.getItem("current_user")).is_superuser?"Admin":"User":"Guest"),[f,d]=_.useState(".short-term"),[h,p]=_.useState(!1),m=x=>{window.scrollTo({top:document.querySelector(x).offsetTop,behavior:"smooth"})},g=document.querySelectorAll("section.short-term, section.mid-term, section.long-term");window.addEventListener("scroll",()=>{let x="";g.forEach(R=>{const E=R.offsetTop,C=R.clientHeight;window.scrollY>=E-C/2&&(x=R.getAttribute("id"))}),d(`.${x}`)});const S=()=>{Ml.logout(),a("/")};return v.jsxs("nav",{children:[v.jsx(Xy,{id:"nav-menu-tooltip",place:"top",effect:"solid",className:"tooltip"}),v.jsxs("ul",{children:[v.jsx("li",{className:"system-icon",children:v.jsx("img",{src:wx,alt:"system-icon"})}),v.jsx("li",{className:f===".short-term"?"active":"",onClick:()=>{(r.pathname.startsWith("/user-management")||r.pathname.startsWith("/account"))&&a("/home"),d(".short-term"),m("#short-term")},children:v.jsx("a",{"data-tooltip-id":"nav-menu-tooltip","data-tooltip-content":"1 year ahead","data-tooltip-offset":30,onClick:()=>{d(".short-term"),m("#short-term")},children:"Short-Term"})}),v.jsx("li",{className:f===".mid-term"?"active":"",onClick:()=>{r.pathname.startsWith("/user-management")&&a("/home"),d(".mid-term"),m("#mid-term")},children:v.jsx("a",{"data-tooltip-id":"nav-menu-tooltip","data-tooltip-content":"3 years ahead","data-tooltip-offset":30,onClick:()=>{d(".mid-term"),m("#mid-term")},children:"Mid-Term"})}),v.jsx("li",{className:f===".long-term"?"active":"",onClick:()=>{r.pathname.startsWith("/user-management")&&a("/home"),d(".long-term"),m("#long-term")},children:v.jsx("a",{"data-tooltip-id":"nav-menu-tooltip","data-tooltip-content":"5 years ahead","data-tooltip-offset":30,onClick:()=>{d(".long-term"),m("#long-term")},children:"Long-Term"})}),s=="Admin"||s=="User"?v.jsxs(v.Fragment,{children:[v.jsx("li",{onClick:l,children:"Upload Dataset"}),v.jsxs("li",{className:f==="profile"?"active":"",children:[v.jsx("img",{src:_u,alt:"sample-profile",onMouseEnter:()=>p(!0),onMouseLeave:()=>p(!1)}),h&&v.jsx("div",{className:`profile-menu-container ${s}`,onMouseEnter:()=>p(!0),onMouseLeave:()=>p(!1),children:v.jsxs("div",{className:"profile-menu",children:[v.jsx("li",{onClick:()=>a("/account"),children:"Edit Account Details"}),JSON.parse(sessionStorage.getItem("current_user")).is_superuser&&v.jsx("li",{onClick:()=>{a("/user-management"),d("profile")},children:"User Management"}),v.jsx("li",{onClick:S,children:"Log Out"})]})})]})]}):v.jsx("button",{disabled:"disabled",className:"view-as-guest",children:"View as Guest"})]})]})}const ju="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Close'%20x='0'%20y='0'%20version='1.1'%20viewBox='0%200%20512%20512'%3e%3cpath%20d='M437.5%20386.6L306.9%20256l130.6-130.6c14.1-14.1%2014.1-36.8%200-50.9-14.1-14.1-36.8-14.1-50.9%200L256%20205.1%20125.4%2074.5c-14.1-14.1-36.8-14.1-50.9%200-14.1%2014.1-14.1%2036.8%200%2050.9L205.1%20256%2074.5%20386.6c-14.1%2014.1-14.1%2036.8%200%2050.9%2014.1%2014.1%2036.8%2014.1%2050.9%200L256%20306.9l130.6%20130.6c14.1%2014.1%2036.8%2014.1%2050.9%200%2014-14.1%2014-36.9%200-50.9z'%20fill='%235a6576'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e",yE="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2024%2024'%20id='Upload'%3e%3cpath%20d='M8.71,7.71,11,5.41V15a1,1,0,0,0,2,0V5.41l2.29,2.3a1,1,0,0,0,1.42,0,1,1,0,0,0,0-1.42l-4-4a1,1,0,0,0-.33-.21,1,1,0,0,0-.76,0,1,1,0,0,0-.33.21l-4,4A1,1,0,1,0,8.71,7.71ZM21,12a1,1,0,0,0-1,1v6a1,1,0,0,1-1,1H5a1,1,0,0,1-1-1V13a1,1,0,0,0-2,0v6a3,3,0,0,0,3,3H19a3,3,0,0,0,3-3V13A1,1,0,0,0,21,12Z'%20fill='%234f46e5'%20class='color000000%20svgShape'%3e%3c/path%3e%3c/svg%3e",gE="http://127.0.0.1:8000/dataset_validation/validate/";class vE{async validate(a){const r=new FormData;r.append("file",a);try{const s=await fetch(gE,{method:"POST",body:r});return s.ok?await s.json():await s.json()}catch(s){console.log("Failed to validate dataset!",s)}}}const bE=new vE,SE="http://127.0.0.1:8000/dataset/";class xE{async postDataset(a){const r=new FormData,s=await Ml.getCurrrentUser();r.append("file",a),r.append("uploaded_by_user",s.id);try{const o=await fetch(SE,{method:"POST",headers:this.getAuthHeader(),body:r});return o.ok?await o.json():await o.json()}catch(o){console.log("Failed to validate dataset!",o)}}getAccessToken(){return localStorage.getItem("access_token")}getAuthHeader(){const a=this.getAccessToken();return{Authorization:a?`JWT ${a}`:""}}}const EE=new xE,vf="http://127.0.0.1:8000/predictions/";class wE{async generatePredictions(a){try{const r=await fetch(`${vf}generate/`,{method:"POST",headers:{...this.getAuthHeader(),"Content-Type":"application/json"},body:JSON.stringify({dataset_id:a})});if(!r.ok){const o=await r.json();throw new Error(o.error||"Failed to generate predictions")}return await r.json()}catch(r){throw console.error("Failed to generate predictions:",r),r}}async getPredictionsByDataset(a){try{const r=await fetch(`${vf}by_dataset/?dataset_id=${a}`,{method:"GET",headers:this.getAuthHeader()});if(!r.ok){const o=await r.json();throw new Error(o.error||"Failed to fetch predictions")}return await r.json()}catch(r){throw console.error("Failed to fetch predictions:",r),r}}async getLatestTrends(){try{let a=[],r=[],s=[];const o=await fetch(`${vf}latest_trends/`,{method:"GET"});if(!o.ok){const h=await o.json();throw new Error(h.error||"Failed to fetch latest trends")}const f=await o.json();f.forEach(h=>{h.category=="growth_rate"?a.push(h):h.category=="revenue"?r.push(h):h.category=="least_crowded"&&s.push(h)});const d=[...new Set(f.map(h=>h.prediction_result.year))];return[a,r,s,d.sort((h,p)=>h-p)]}catch(a){throw console.error("Failed to fetch latest trends:",a),a}}getAccessToken(){return localStorage.getItem("access_token")}getAuthHeader(){const a=this.getAccessToken();return{Authorization:a?`JWT ${a}`:""}}}const Zy=new wE,AE="/assets/analysis-chart-DyDYoyz_.gif";function _E({showModal:l,onPredictionComplete:a}){const[r,s]=_.useState(!1),[o,f]=_.useState(null),[d,h]=_.useState(String),[p,m]=_.useState(""),[g,S]=_.useState(!1),[x,R]=_.useState(!1),E=v.jsxs("div",{className:"dataset-requirements",children:["Must include exactly the following columns and data types:",v.jsx("br",{}),v.jsx("br",{}),v.jsxs("table",{children:[v.jsxs("tr",{children:[v.jsx("th",{children:"Column Name"}),v.jsx("th",{children:"Data Type"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Year"}),v.jsx("td",{children:"Integer"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Industry Sector"}),v.jsx("td",{children:"String"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Number of Businesses"}),v.jsx("td",{children:"Number"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Revenue (PHP Millions)"}),v.jsx("td",{children:"Number"})]}),v.jsxs("tr",{children:[v.jsx("td",{children:"Growth Rate (%)"}),v.jsx("td",{children:"Number"})]})]})]}),C=T=>{if(T.target.files.length>0){const M=T.target.files[0];f(M)}else f(null)},D=T=>{h(null),T.preventDefault(),T.stopPropagation(),document.getElementById("file").value="",f(null)},w=async()=>{S(!1),R(!0);try{const T=await bE.validate(o);if(h(T),console.log(T),T.valid){m("Uploading dataset...");const M=await EE.postDataset(o);if(M.id){m("Generating predictions... This may take a few moments.");const X=await Zy.generatePredictions(M.id);if(X.success)S(!0),m(`Successfully generated ${X.predictions_count} predictions for your dataset!`),h({success:!0,valid:!0,message:`Successfully generated ${X.predictions_count} predictions for your dataset!`}),a&&a(M.id,X),setTimeout(()=>{S(!1),l()},2e3);else throw new Error("Failed to generate predictions")}else throw new Error("Failed to upload dataset")}}catch(T){console.error("Error in submission process:",T),h({valid:!1,message:`Error: ${T.message||"An unexpected error occurred"}`}),m("")}finally{R(!1)}};return v.jsxs("div",{className:"upload-dataset-modal",children:[v.jsx("section",{className:"overlay",onClick:l}),v.jsx(Xy,{id:"dataset-required-info",place:"bottom",effect:"solid",className:"tooltip",children:E}),v.jsxs("section",{className:"content",children:[v.jsx("button",{onMouseEnter:()=>s(!0),onMouseLeave:()=>s(!1),onClick:l,children:v.jsx("img",{src:r?zu:ju,alt:"close-icon"})}),v.jsx("h2",{children:"Upload Dataset"}),v.jsxs("p",{className:"reminder",children:["Please make sure the dataset is correct and complete to avoid inaccurate result. ","",v.jsx("a",{"data-tooltip-id":"dataset-required-info","data-tooltip-offset":10,children:"Hover to see Dataset Requirements."})]}),p&&v.jsxs("div",{className:`current-step ${g?"success":"processing"}`,children:[v.jsx("img",{src:AE,alt:""}),v.jsx("p",{children:p})]}),d&&d.valid===!1&&v.jsx("div",{className:`response-message ${d.valid?"success":"error"}`,children:d.message}),p===""&&v.jsxs("section",{children:[v.jsx("input",{type:"file",name:"file",id:"file",accept:".csv",style:{display:"none"},onChange:C}),v.jsxs("label",{htmlFor:"file",children:[v.jsx("img",{src:yE,alt:"upload-icon",className:"upload-icon"}),v.jsxs("p",{children:[v.jsx("span",{children:"Click here"})," to upload your file."]}),v.jsx("p",{className:"supported-format",children:"Supported Format: csv"}),o&&v.jsxs("div",{className:"selected-file",children:[v.jsx("p",{children:o.name}),v.jsx("img",{src:ju,alt:"remove",className:"remove-icon",onClick:D})]})]})]}),v.jsxs("button",{disabled:o===null||x,className:"submit-button",onClick:w,children:[x&&v.jsx(Di,{}),x?"Processing...":"Make Prediction"]})]})]})}function Vt({color:l=null,data:a,topNumber:r,type:s,filterResult:o}){const[f,d]=_.useState(!1);_.useEffect(()=>{r!=1&&f?document.getElementById(`card-top-1-${s}`).classList.remove("active"):r!=1&&!f&&document.getElementById(`card-top-1-${s}`).classList.add("active")},[f]);const h=p=>p>=1e9?(p/1e9).toFixed(2)+" billiion":p>=1e6?(p/1e6).toFixed(2)+" million":p.toLocaleString();return v.jsxs("article",{id:`card-top-${r}-${s}`,className:`card-top-${r} ${r==1?"active":""} ${l??""}`,onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1),children:[v.jsx("div",{className:`circle ${r==1?"active":""} ${l??""}`}),v.jsxs("div",{className:`info-container ${r==1?"active":""} ${l??""}`,children:[v.jsx("h1",{children:r}),v.jsxs("div",{className:"paragraph-container",children:[o=="Growing Industry Sector"&&v.jsxs("p",{children:["By ",a?a.prediction_result.year:"___",", the"," ",a?a.prediction_result.industry_sector:"___"," sector is projected to achieve a growth rate of"," ",a?a.prediction_result.predicted_growth_rate:"___","%, which marks an increase of"," ",v.jsx("span",{className:"positive",children:"****"})," percentage points from 15% in 2025, based on current historical trend analysis."]}),o=="Industry Sector Revenue"&&v.jsxs("p",{children:["By ",a?a.prediction_result.year:"___",", the"," ",a?a.prediction_result.industry_sector:"___"," sector is expected to generate approximately ₱",a?h(Number(a.prediction_result.predicted_revenue)):"___"," ","in revenue, reflecting a 12.8% increase from ₱461 billion in 2025, based on the current historical trend analysis."]})]}),o=="Least Crowded"&&v.jsxs("p",{children:["By ",a?a.prediction_result.year:"___",", the"," ",a?a.prediction_result.industry_sector:"___"," sector is projected to be the least crowded industry, with an estimated"," ",a?h(a.prediction_result.predicted_least_crowded):"___"," ","businesses in operation, reflecting an 8% increase from 12,000 in 2025, based on the current historical trend analysis."]}),v.jsx("h1",{className:`industry ${r==1?"active":""}`,children:a?a.prediction_result.industry_sector:"___"})]})]})}function Qy(l,a){return function(){return l.apply(a,arguments)}}const{toString:TE}=Object.prototype,{getPrototypeOf:od}=Object,{iterator:Hu,toStringTag:Ky}=Symbol,qu=(l=>a=>{const r=TE.call(a);return l[r]||(l[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),Fn=l=>(l=l.toLowerCase(),a=>qu(a)===l),Vu=l=>a=>typeof a===l,{isArray:Ni}=Array,Vr=Vu("undefined");function RE(l){return l!==null&&!Vr(l)&&l.constructor!==null&&!Vr(l.constructor)&&en(l.constructor.isBuffer)&&l.constructor.isBuffer(l)}const Jy=Fn("ArrayBuffer");function OE(l){let a;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?a=ArrayBuffer.isView(l):a=l&&l.buffer&&Jy(l.buffer),a}const jE=Vu("string"),en=Vu("function"),Py=Vu("number"),Fu=l=>l!==null&&typeof l=="object",DE=l=>l===!0||l===!1,yu=l=>{if(qu(l)!=="object")return!1;const a=od(l);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(Ky in l)&&!(Hu in l)},CE=Fn("Date"),NE=Fn("File"),ME=Fn("Blob"),UE=Fn("FileList"),zE=l=>Fu(l)&&en(l.pipe),LE=l=>{let a;return l&&(typeof FormData=="function"&&l instanceof FormData||en(l.append)&&((a=qu(l))==="formdata"||a==="object"&&en(l.toString)&&l.toString()==="[object FormData]"))},BE=Fn("URLSearchParams"),[kE,HE,qE,VE]=["ReadableStream","Request","Response","Headers"].map(Fn),FE=l=>l.trim?l.trim():l.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Jr(l,a,{allOwnKeys:r=!1}={}){if(l===null||typeof l>"u")return;let s,o;if(typeof l!="object"&&(l=[l]),Ni(l))for(s=0,o=l.length;s<o;s++)a.call(null,l[s],s,l);else{const f=r?Object.getOwnPropertyNames(l):Object.keys(l),d=f.length;let h;for(s=0;s<d;s++)h=f[s],a.call(null,l[h],h,l)}}function Wy(l,a){a=a.toLowerCase();const r=Object.keys(l);let s=r.length,o;for(;s-- >0;)if(o=r[s],a===o.toLowerCase())return o;return null}const ja=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Iy=l=>!Vr(l)&&l!==ja;function Uf(){const{caseless:l}=Iy(this)&&this||{},a={},r=(s,o)=>{const f=l&&Wy(a,o)||o;yu(a[f])&&yu(s)?a[f]=Uf(a[f],s):yu(s)?a[f]=Uf({},s):Ni(s)?a[f]=s.slice():a[f]=s};for(let s=0,o=arguments.length;s<o;s++)arguments[s]&&Jr(arguments[s],r);return a}const YE=(l,a,r,{allOwnKeys:s}={})=>(Jr(a,(o,f)=>{r&&en(o)?l[f]=Qy(o,r):l[f]=o},{allOwnKeys:s}),l),GE=l=>(l.charCodeAt(0)===65279&&(l=l.slice(1)),l),$E=(l,a,r,s)=>{l.prototype=Object.create(a.prototype,s),l.prototype.constructor=l,Object.defineProperty(l,"super",{value:a.prototype}),r&&Object.assign(l.prototype,r)},XE=(l,a,r,s)=>{let o,f,d;const h={};if(a=a||{},l==null)return a;do{for(o=Object.getOwnPropertyNames(l),f=o.length;f-- >0;)d=o[f],(!s||s(d,l,a))&&!h[d]&&(a[d]=l[d],h[d]=!0);l=r!==!1&&od(l)}while(l&&(!r||r(l,a))&&l!==Object.prototype);return a},ZE=(l,a,r)=>{l=String(l),(r===void 0||r>l.length)&&(r=l.length),r-=a.length;const s=l.indexOf(a,r);return s!==-1&&s===r},QE=l=>{if(!l)return null;if(Ni(l))return l;let a=l.length;if(!Py(a))return null;const r=new Array(a);for(;a-- >0;)r[a]=l[a];return r},KE=(l=>a=>l&&a instanceof l)(typeof Uint8Array<"u"&&od(Uint8Array)),JE=(l,a)=>{const s=(l&&l[Hu]).call(l);let o;for(;(o=s.next())&&!o.done;){const f=o.value;a.call(l,f[0],f[1])}},PE=(l,a)=>{let r;const s=[];for(;(r=l.exec(a))!==null;)s.push(r);return s},WE=Fn("HTMLFormElement"),IE=l=>l.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,s,o){return s.toUpperCase()+o}),Y0=(({hasOwnProperty:l})=>(a,r)=>l.call(a,r))(Object.prototype),e2=Fn("RegExp"),eg=(l,a)=>{const r=Object.getOwnPropertyDescriptors(l),s={};Jr(r,(o,f)=>{let d;(d=a(o,f,l))!==!1&&(s[f]=d||o)}),Object.defineProperties(l,s)},t2=l=>{eg(l,(a,r)=>{if(en(l)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const s=l[r];if(en(s)){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},n2=(l,a)=>{const r={},s=o=>{o.forEach(f=>{r[f]=!0})};return Ni(l)?s(l):s(String(l).split(a)),r},l2=()=>{},a2=(l,a)=>l!=null&&Number.isFinite(l=+l)?l:a;function i2(l){return!!(l&&en(l.append)&&l[Ky]==="FormData"&&l[Hu])}const r2=l=>{const a=new Array(10),r=(s,o)=>{if(Fu(s)){if(a.indexOf(s)>=0)return;if(!("toJSON"in s)){a[o]=s;const f=Ni(s)?[]:{};return Jr(s,(d,h)=>{const p=r(d,o+1);!Vr(p)&&(f[h]=p)}),a[o]=void 0,f}}return s};return r(l,0)},s2=Fn("AsyncFunction"),u2=l=>l&&(Fu(l)||en(l))&&en(l.then)&&en(l.catch),tg=((l,a)=>l?setImmediate:a?((r,s)=>(ja.addEventListener("message",({source:o,data:f})=>{o===ja&&f===r&&s.length&&s.shift()()},!1),o=>{s.push(o),ja.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",en(ja.postMessage)),o2=typeof queueMicrotask<"u"?queueMicrotask.bind(ja):typeof process<"u"&&process.nextTick||tg,c2=l=>l!=null&&en(l[Hu]),F={isArray:Ni,isArrayBuffer:Jy,isBuffer:RE,isFormData:LE,isArrayBufferView:OE,isString:jE,isNumber:Py,isBoolean:DE,isObject:Fu,isPlainObject:yu,isReadableStream:kE,isRequest:HE,isResponse:qE,isHeaders:VE,isUndefined:Vr,isDate:CE,isFile:NE,isBlob:ME,isRegExp:e2,isFunction:en,isStream:zE,isURLSearchParams:BE,isTypedArray:KE,isFileList:UE,forEach:Jr,merge:Uf,extend:YE,trim:FE,stripBOM:GE,inherits:$E,toFlatObject:XE,kindOf:qu,kindOfTest:Fn,endsWith:ZE,toArray:QE,forEachEntry:JE,matchAll:PE,isHTMLForm:WE,hasOwnProperty:Y0,hasOwnProp:Y0,reduceDescriptors:eg,freezeMethods:t2,toObjectSet:n2,toCamelCase:IE,noop:l2,toFiniteNumber:a2,findKey:Wy,global:ja,isContextDefined:Iy,isSpecCompliantForm:i2,toJSONObject:r2,isAsyncFn:s2,isThenable:u2,setImmediate:tg,asap:o2,isIterable:c2};function Re(l,a,r,s,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=l,this.name="AxiosError",a&&(this.code=a),r&&(this.config=r),s&&(this.request=s),o&&(this.response=o,this.status=o.status?o.status:null)}F.inherits(Re,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:F.toJSONObject(this.config),code:this.code,status:this.status}}});const ng=Re.prototype,lg={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(l=>{lg[l]={value:l}});Object.defineProperties(Re,lg);Object.defineProperty(ng,"isAxiosError",{value:!0});Re.from=(l,a,r,s,o,f)=>{const d=Object.create(ng);return F.toFlatObject(l,d,function(p){return p!==Error.prototype},h=>h!=="isAxiosError"),Re.call(d,l.message,a,r,s,o),d.cause=l,d.name=l.name,f&&Object.assign(d,f),d};const f2=null;function zf(l){return F.isPlainObject(l)||F.isArray(l)}function ag(l){return F.endsWith(l,"[]")?l.slice(0,-2):l}function G0(l,a,r){return l?l.concat(a).map(function(o,f){return o=ag(o),!r&&f?"["+o+"]":o}).join(r?".":""):a}function d2(l){return F.isArray(l)&&!l.some(zf)}const h2=F.toFlatObject(F,{},null,function(a){return/^is[A-Z]/.test(a)});function Yu(l,a,r){if(!F.isObject(l))throw new TypeError("target must be an object");a=a||new FormData,r=F.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(C,D){return!F.isUndefined(D[C])});const s=r.metaTokens,o=r.visitor||g,f=r.dots,d=r.indexes,p=(r.Blob||typeof Blob<"u"&&Blob)&&F.isSpecCompliantForm(a);if(!F.isFunction(o))throw new TypeError("visitor must be a function");function m(E){if(E===null)return"";if(F.isDate(E))return E.toISOString();if(!p&&F.isBlob(E))throw new Re("Blob is not supported. Use a Buffer instead.");return F.isArrayBuffer(E)||F.isTypedArray(E)?p&&typeof Blob=="function"?new Blob([E]):Buffer.from(E):E}function g(E,C,D){let w=E;if(E&&!D&&typeof E=="object"){if(F.endsWith(C,"{}"))C=s?C:C.slice(0,-2),E=JSON.stringify(E);else if(F.isArray(E)&&d2(E)||(F.isFileList(E)||F.endsWith(C,"[]"))&&(w=F.toArray(E)))return C=ag(C),w.forEach(function(M,X){!(F.isUndefined(M)||M===null)&&a.append(d===!0?G0([C],X,f):d===null?C:C+"[]",m(M))}),!1}return zf(E)?!0:(a.append(G0(D,C,f),m(E)),!1)}const S=[],x=Object.assign(h2,{defaultVisitor:g,convertValue:m,isVisitable:zf});function R(E,C){if(!F.isUndefined(E)){if(S.indexOf(E)!==-1)throw Error("Circular reference detected in "+C.join("."));S.push(E),F.forEach(E,function(w,T){(!(F.isUndefined(w)||w===null)&&o.call(a,w,F.isString(T)?T.trim():T,C,x))===!0&&R(w,C?C.concat(T):[T])}),S.pop()}}if(!F.isObject(l))throw new TypeError("data must be an object");return R(l),a}function $0(l){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(l).replace(/[!'()~]|%20|%00/g,function(s){return a[s]})}function cd(l,a){this._pairs=[],l&&Yu(l,this,a)}const ig=cd.prototype;ig.append=function(a,r){this._pairs.push([a,r])};ig.toString=function(a){const r=a?function(s){return a.call(this,s,$0)}:$0;return this._pairs.map(function(o){return r(o[0])+"="+r(o[1])},"").join("&")};function m2(l){return encodeURIComponent(l).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function rg(l,a,r){if(!a)return l;const s=r&&r.encode||m2;F.isFunction(r)&&(r={serialize:r});const o=r&&r.serialize;let f;if(o?f=o(a,r):f=F.isURLSearchParams(a)?a.toString():new cd(a,r).toString(s),f){const d=l.indexOf("#");d!==-1&&(l=l.slice(0,d)),l+=(l.indexOf("?")===-1?"?":"&")+f}return l}class X0{constructor(){this.handlers=[]}use(a,r,s){return this.handlers.push({fulfilled:a,rejected:r,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){F.forEach(this.handlers,function(s){s!==null&&a(s)})}}const sg={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},p2=typeof URLSearchParams<"u"?URLSearchParams:cd,y2=typeof FormData<"u"?FormData:null,g2=typeof Blob<"u"?Blob:null,v2={isBrowser:!0,classes:{URLSearchParams:p2,FormData:y2,Blob:g2},protocols:["http","https","file","blob","url","data"]},fd=typeof window<"u"&&typeof document<"u",Lf=typeof navigator=="object"&&navigator||void 0,b2=fd&&(!Lf||["ReactNative","NativeScript","NS"].indexOf(Lf.product)<0),S2=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",x2=fd&&window.location.href||"http://localhost",E2=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:fd,hasStandardBrowserEnv:b2,hasStandardBrowserWebWorkerEnv:S2,navigator:Lf,origin:x2},Symbol.toStringTag,{value:"Module"})),kt={...E2,...v2};function w2(l,a){return Yu(l,new kt.classes.URLSearchParams,Object.assign({visitor:function(r,s,o,f){return kt.isNode&&F.isBuffer(r)?(this.append(s,r.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},a))}function A2(l){return F.matchAll(/\w+|\[(\w*)]/g,l).map(a=>a[0]==="[]"?"":a[1]||a[0])}function _2(l){const a={},r=Object.keys(l);let s;const o=r.length;let f;for(s=0;s<o;s++)f=r[s],a[f]=l[f];return a}function ug(l){function a(r,s,o,f){let d=r[f++];if(d==="__proto__")return!0;const h=Number.isFinite(+d),p=f>=r.length;return d=!d&&F.isArray(o)?o.length:d,p?(F.hasOwnProp(o,d)?o[d]=[o[d],s]:o[d]=s,!h):((!o[d]||!F.isObject(o[d]))&&(o[d]=[]),a(r,s,o[d],f)&&F.isArray(o[d])&&(o[d]=_2(o[d])),!h)}if(F.isFormData(l)&&F.isFunction(l.entries)){const r={};return F.forEachEntry(l,(s,o)=>{a(A2(s),o,r,0)}),r}return null}function T2(l,a,r){if(F.isString(l))try{return(a||JSON.parse)(l),F.trim(l)}catch(s){if(s.name!=="SyntaxError")throw s}return(r||JSON.stringify)(l)}const Pr={transitional:sg,adapter:["xhr","http","fetch"],transformRequest:[function(a,r){const s=r.getContentType()||"",o=s.indexOf("application/json")>-1,f=F.isObject(a);if(f&&F.isHTMLForm(a)&&(a=new FormData(a)),F.isFormData(a))return o?JSON.stringify(ug(a)):a;if(F.isArrayBuffer(a)||F.isBuffer(a)||F.isStream(a)||F.isFile(a)||F.isBlob(a)||F.isReadableStream(a))return a;if(F.isArrayBufferView(a))return a.buffer;if(F.isURLSearchParams(a))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();let h;if(f){if(s.indexOf("application/x-www-form-urlencoded")>-1)return w2(a,this.formSerializer).toString();if((h=F.isFileList(a))||s.indexOf("multipart/form-data")>-1){const p=this.env&&this.env.FormData;return Yu(h?{"files[]":a}:a,p&&new p,this.formSerializer)}}return f||o?(r.setContentType("application/json",!1),T2(a)):a}],transformResponse:[function(a){const r=this.transitional||Pr.transitional,s=r&&r.forcedJSONParsing,o=this.responseType==="json";if(F.isResponse(a)||F.isReadableStream(a))return a;if(a&&F.isString(a)&&(s&&!this.responseType||o)){const d=!(r&&r.silentJSONParsing)&&o;try{return JSON.parse(a)}catch(h){if(d)throw h.name==="SyntaxError"?Re.from(h,Re.ERR_BAD_RESPONSE,this,null,this.response):h}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:kt.classes.FormData,Blob:kt.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};F.forEach(["delete","get","head","post","put","patch"],l=>{Pr.headers[l]={}});const R2=F.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),O2=l=>{const a={};let r,s,o;return l&&l.split(`
`).forEach(function(d){o=d.indexOf(":"),r=d.substring(0,o).trim().toLowerCase(),s=d.substring(o+1).trim(),!(!r||a[r]&&R2[r])&&(r==="set-cookie"?a[r]?a[r].push(s):a[r]=[s]:a[r]=a[r]?a[r]+", "+s:s)}),a},Z0=Symbol("internals");function Ur(l){return l&&String(l).trim().toLowerCase()}function gu(l){return l===!1||l==null?l:F.isArray(l)?l.map(gu):String(l)}function j2(l){const a=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=r.exec(l);)a[s[1]]=s[2];return a}const D2=l=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(l.trim());function bf(l,a,r,s,o){if(F.isFunction(s))return s.call(this,a,r);if(o&&(a=r),!!F.isString(a)){if(F.isString(s))return a.indexOf(s)!==-1;if(F.isRegExp(s))return s.test(a)}}function C2(l){return l.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,r,s)=>r.toUpperCase()+s)}function N2(l,a){const r=F.toCamelCase(" "+a);["get","set","has"].forEach(s=>{Object.defineProperty(l,s+r,{value:function(o,f,d){return this[s].call(this,a,o,f,d)},configurable:!0})})}let tn=class{constructor(a){a&&this.set(a)}set(a,r,s){const o=this;function f(h,p,m){const g=Ur(p);if(!g)throw new Error("header name must be a non-empty string");const S=F.findKey(o,g);(!S||o[S]===void 0||m===!0||m===void 0&&o[S]!==!1)&&(o[S||p]=gu(h))}const d=(h,p)=>F.forEach(h,(m,g)=>f(m,g,p));if(F.isPlainObject(a)||a instanceof this.constructor)d(a,r);else if(F.isString(a)&&(a=a.trim())&&!D2(a))d(O2(a),r);else if(F.isObject(a)&&F.isIterable(a)){let h={},p,m;for(const g of a){if(!F.isArray(g))throw TypeError("Object iterator must return a key-value pair");h[m=g[0]]=(p=h[m])?F.isArray(p)?[...p,g[1]]:[p,g[1]]:g[1]}d(h,r)}else a!=null&&f(r,a,s);return this}get(a,r){if(a=Ur(a),a){const s=F.findKey(this,a);if(s){const o=this[s];if(!r)return o;if(r===!0)return j2(o);if(F.isFunction(r))return r.call(this,o,s);if(F.isRegExp(r))return r.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(a,r){if(a=Ur(a),a){const s=F.findKey(this,a);return!!(s&&this[s]!==void 0&&(!r||bf(this,this[s],s,r)))}return!1}delete(a,r){const s=this;let o=!1;function f(d){if(d=Ur(d),d){const h=F.findKey(s,d);h&&(!r||bf(s,s[h],h,r))&&(delete s[h],o=!0)}}return F.isArray(a)?a.forEach(f):f(a),o}clear(a){const r=Object.keys(this);let s=r.length,o=!1;for(;s--;){const f=r[s];(!a||bf(this,this[f],f,a,!0))&&(delete this[f],o=!0)}return o}normalize(a){const r=this,s={};return F.forEach(this,(o,f)=>{const d=F.findKey(s,f);if(d){r[d]=gu(o),delete r[f];return}const h=a?C2(f):String(f).trim();h!==f&&delete r[f],r[h]=gu(o),s[h]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){const r=Object.create(null);return F.forEach(this,(s,o)=>{s!=null&&s!==!1&&(r[o]=a&&F.isArray(s)?s.join(", "):s)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,r])=>a+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...r){const s=new this(a);return r.forEach(o=>s.set(o)),s}static accessor(a){const s=(this[Z0]=this[Z0]={accessors:{}}).accessors,o=this.prototype;function f(d){const h=Ur(d);s[h]||(N2(o,d),s[h]=!0)}return F.isArray(a)?a.forEach(f):f(a),this}};tn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);F.reduceDescriptors(tn.prototype,({value:l},a)=>{let r=a[0].toUpperCase()+a.slice(1);return{get:()=>l,set(s){this[r]=s}}});F.freezeMethods(tn);function Sf(l,a){const r=this||Pr,s=a||r,o=tn.from(s.headers);let f=s.data;return F.forEach(l,function(h){f=h.call(r,f,o.normalize(),a?a.status:void 0)}),o.normalize(),f}function og(l){return!!(l&&l.__CANCEL__)}function Mi(l,a,r){Re.call(this,l??"canceled",Re.ERR_CANCELED,a,r),this.name="CanceledError"}F.inherits(Mi,Re,{__CANCEL__:!0});function cg(l,a,r){const s=r.config.validateStatus;!r.status||!s||s(r.status)?l(r):a(new Re("Request failed with status code "+r.status,[Re.ERR_BAD_REQUEST,Re.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function M2(l){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(l);return a&&a[1]||""}function U2(l,a){l=l||10;const r=new Array(l),s=new Array(l);let o=0,f=0,d;return a=a!==void 0?a:1e3,function(p){const m=Date.now(),g=s[f];d||(d=m),r[o]=p,s[o]=m;let S=f,x=0;for(;S!==o;)x+=r[S++],S=S%l;if(o=(o+1)%l,o===f&&(f=(f+1)%l),m-d<a)return;const R=g&&m-g;return R?Math.round(x*1e3/R):void 0}}function z2(l,a){let r=0,s=1e3/a,o,f;const d=(m,g=Date.now())=>{r=g,o=null,f&&(clearTimeout(f),f=null),l.apply(null,m)};return[(...m)=>{const g=Date.now(),S=g-r;S>=s?d(m,g):(o=m,f||(f=setTimeout(()=>{f=null,d(o)},s-S)))},()=>o&&d(o)]}const Du=(l,a,r=3)=>{let s=0;const o=U2(50,250);return z2(f=>{const d=f.loaded,h=f.lengthComputable?f.total:void 0,p=d-s,m=o(p),g=d<=h;s=d;const S={loaded:d,total:h,progress:h?d/h:void 0,bytes:p,rate:m||void 0,estimated:m&&h&&g?(h-d)/m:void 0,event:f,lengthComputable:h!=null,[a?"download":"upload"]:!0};l(S)},r)},Q0=(l,a)=>{const r=l!=null;return[s=>a[0]({lengthComputable:r,total:l,loaded:s}),a[1]]},K0=l=>(...a)=>F.asap(()=>l(...a)),L2=kt.hasStandardBrowserEnv?((l,a)=>r=>(r=new URL(r,kt.origin),l.protocol===r.protocol&&l.host===r.host&&(a||l.port===r.port)))(new URL(kt.origin),kt.navigator&&/(msie|trident)/i.test(kt.navigator.userAgent)):()=>!0,B2=kt.hasStandardBrowserEnv?{write(l,a,r,s,o,f){const d=[l+"="+encodeURIComponent(a)];F.isNumber(r)&&d.push("expires="+new Date(r).toGMTString()),F.isString(s)&&d.push("path="+s),F.isString(o)&&d.push("domain="+o),f===!0&&d.push("secure"),document.cookie=d.join("; ")},read(l){const a=document.cookie.match(new RegExp("(^|;\\s*)("+l+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(l){this.write(l,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function k2(l){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(l)}function H2(l,a){return a?l.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):l}function fg(l,a,r){let s=!k2(a);return l&&(s||r==!1)?H2(l,a):a}const J0=l=>l instanceof tn?{...l}:l;function za(l,a){a=a||{};const r={};function s(m,g,S,x){return F.isPlainObject(m)&&F.isPlainObject(g)?F.merge.call({caseless:x},m,g):F.isPlainObject(g)?F.merge({},g):F.isArray(g)?g.slice():g}function o(m,g,S,x){if(F.isUndefined(g)){if(!F.isUndefined(m))return s(void 0,m,S,x)}else return s(m,g,S,x)}function f(m,g){if(!F.isUndefined(g))return s(void 0,g)}function d(m,g){if(F.isUndefined(g)){if(!F.isUndefined(m))return s(void 0,m)}else return s(void 0,g)}function h(m,g,S){if(S in a)return s(m,g);if(S in l)return s(void 0,m)}const p={url:f,method:f,data:f,baseURL:d,transformRequest:d,transformResponse:d,paramsSerializer:d,timeout:d,timeoutMessage:d,withCredentials:d,withXSRFToken:d,adapter:d,responseType:d,xsrfCookieName:d,xsrfHeaderName:d,onUploadProgress:d,onDownloadProgress:d,decompress:d,maxContentLength:d,maxBodyLength:d,beforeRedirect:d,transport:d,httpAgent:d,httpsAgent:d,cancelToken:d,socketPath:d,responseEncoding:d,validateStatus:h,headers:(m,g,S)=>o(J0(m),J0(g),S,!0)};return F.forEach(Object.keys(Object.assign({},l,a)),function(g){const S=p[g]||o,x=S(l[g],a[g],g);F.isUndefined(x)&&S!==h||(r[g]=x)}),r}const dg=l=>{const a=za({},l);let{data:r,withXSRFToken:s,xsrfHeaderName:o,xsrfCookieName:f,headers:d,auth:h}=a;a.headers=d=tn.from(d),a.url=rg(fg(a.baseURL,a.url,a.allowAbsoluteUrls),l.params,l.paramsSerializer),h&&d.set("Authorization","Basic "+btoa((h.username||"")+":"+(h.password?unescape(encodeURIComponent(h.password)):"")));let p;if(F.isFormData(r)){if(kt.hasStandardBrowserEnv||kt.hasStandardBrowserWebWorkerEnv)d.setContentType(void 0);else if((p=d.getContentType())!==!1){const[m,...g]=p?p.split(";").map(S=>S.trim()).filter(Boolean):[];d.setContentType([m||"multipart/form-data",...g].join("; "))}}if(kt.hasStandardBrowserEnv&&(s&&F.isFunction(s)&&(s=s(a)),s||s!==!1&&L2(a.url))){const m=o&&f&&B2.read(f);m&&d.set(o,m)}return a},q2=typeof XMLHttpRequest<"u",V2=q2&&function(l){return new Promise(function(r,s){const o=dg(l);let f=o.data;const d=tn.from(o.headers).normalize();let{responseType:h,onUploadProgress:p,onDownloadProgress:m}=o,g,S,x,R,E;function C(){R&&R(),E&&E(),o.cancelToken&&o.cancelToken.unsubscribe(g),o.signal&&o.signal.removeEventListener("abort",g)}let D=new XMLHttpRequest;D.open(o.method.toUpperCase(),o.url,!0),D.timeout=o.timeout;function w(){if(!D)return;const M=tn.from("getAllResponseHeaders"in D&&D.getAllResponseHeaders()),V={data:!h||h==="text"||h==="json"?D.responseText:D.response,status:D.status,statusText:D.statusText,headers:M,config:l,request:D};cg(function(W){r(W),C()},function(W){s(W),C()},V),D=null}"onloadend"in D?D.onloadend=w:D.onreadystatechange=function(){!D||D.readyState!==4||D.status===0&&!(D.responseURL&&D.responseURL.indexOf("file:")===0)||setTimeout(w)},D.onabort=function(){D&&(s(new Re("Request aborted",Re.ECONNABORTED,l,D)),D=null)},D.onerror=function(){s(new Re("Network Error",Re.ERR_NETWORK,l,D)),D=null},D.ontimeout=function(){let X=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const V=o.transitional||sg;o.timeoutErrorMessage&&(X=o.timeoutErrorMessage),s(new Re(X,V.clarifyTimeoutError?Re.ETIMEDOUT:Re.ECONNABORTED,l,D)),D=null},f===void 0&&d.setContentType(null),"setRequestHeader"in D&&F.forEach(d.toJSON(),function(X,V){D.setRequestHeader(V,X)}),F.isUndefined(o.withCredentials)||(D.withCredentials=!!o.withCredentials),h&&h!=="json"&&(D.responseType=o.responseType),m&&([x,E]=Du(m,!0),D.addEventListener("progress",x)),p&&D.upload&&([S,R]=Du(p),D.upload.addEventListener("progress",S),D.upload.addEventListener("loadend",R)),(o.cancelToken||o.signal)&&(g=M=>{D&&(s(!M||M.type?new Mi(null,l,D):M),D.abort(),D=null)},o.cancelToken&&o.cancelToken.subscribe(g),o.signal&&(o.signal.aborted?g():o.signal.addEventListener("abort",g)));const T=M2(o.url);if(T&&kt.protocols.indexOf(T)===-1){s(new Re("Unsupported protocol "+T+":",Re.ERR_BAD_REQUEST,l));return}D.send(f||null)})},F2=(l,a)=>{const{length:r}=l=l?l.filter(Boolean):[];if(a||r){let s=new AbortController,o;const f=function(m){if(!o){o=!0,h();const g=m instanceof Error?m:this.reason;s.abort(g instanceof Re?g:new Mi(g instanceof Error?g.message:g))}};let d=a&&setTimeout(()=>{d=null,f(new Re(`timeout ${a} of ms exceeded`,Re.ETIMEDOUT))},a);const h=()=>{l&&(d&&clearTimeout(d),d=null,l.forEach(m=>{m.unsubscribe?m.unsubscribe(f):m.removeEventListener("abort",f)}),l=null)};l.forEach(m=>m.addEventListener("abort",f));const{signal:p}=s;return p.unsubscribe=()=>F.asap(h),p}},Y2=function*(l,a){let r=l.byteLength;if(r<a){yield l;return}let s=0,o;for(;s<r;)o=s+a,yield l.slice(s,o),s=o},G2=async function*(l,a){for await(const r of $2(l))yield*Y2(r,a)},$2=async function*(l){if(l[Symbol.asyncIterator]){yield*l;return}const a=l.getReader();try{for(;;){const{done:r,value:s}=await a.read();if(r)break;yield s}}finally{await a.cancel()}},P0=(l,a,r,s)=>{const o=G2(l,a);let f=0,d,h=p=>{d||(d=!0,s&&s(p))};return new ReadableStream({async pull(p){try{const{done:m,value:g}=await o.next();if(m){h(),p.close();return}let S=g.byteLength;if(r){let x=f+=S;r(x)}p.enqueue(new Uint8Array(g))}catch(m){throw h(m),m}},cancel(p){return h(p),o.return()}},{highWaterMark:2})},Gu=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",hg=Gu&&typeof ReadableStream=="function",X2=Gu&&(typeof TextEncoder=="function"?(l=>a=>l.encode(a))(new TextEncoder):async l=>new Uint8Array(await new Response(l).arrayBuffer())),mg=(l,...a)=>{try{return!!l(...a)}catch{return!1}},Z2=hg&&mg(()=>{let l=!1;const a=new Request(kt.origin,{body:new ReadableStream,method:"POST",get duplex(){return l=!0,"half"}}).headers.has("Content-Type");return l&&!a}),W0=64*1024,Bf=hg&&mg(()=>F.isReadableStream(new Response("").body)),Cu={stream:Bf&&(l=>l.body)};Gu&&(l=>{["text","arrayBuffer","blob","formData","stream"].forEach(a=>{!Cu[a]&&(Cu[a]=F.isFunction(l[a])?r=>r[a]():(r,s)=>{throw new Re(`Response type '${a}' is not supported`,Re.ERR_NOT_SUPPORT,s)})})})(new Response);const Q2=async l=>{if(l==null)return 0;if(F.isBlob(l))return l.size;if(F.isSpecCompliantForm(l))return(await new Request(kt.origin,{method:"POST",body:l}).arrayBuffer()).byteLength;if(F.isArrayBufferView(l)||F.isArrayBuffer(l))return l.byteLength;if(F.isURLSearchParams(l)&&(l=l+""),F.isString(l))return(await X2(l)).byteLength},K2=async(l,a)=>{const r=F.toFiniteNumber(l.getContentLength());return r??Q2(a)},J2=Gu&&(async l=>{let{url:a,method:r,data:s,signal:o,cancelToken:f,timeout:d,onDownloadProgress:h,onUploadProgress:p,responseType:m,headers:g,withCredentials:S="same-origin",fetchOptions:x}=dg(l);m=m?(m+"").toLowerCase():"text";let R=F2([o,f&&f.toAbortSignal()],d),E;const C=R&&R.unsubscribe&&(()=>{R.unsubscribe()});let D;try{if(p&&Z2&&r!=="get"&&r!=="head"&&(D=await K2(g,s))!==0){let V=new Request(a,{method:"POST",body:s,duplex:"half"}),I;if(F.isFormData(s)&&(I=V.headers.get("content-type"))&&g.setContentType(I),V.body){const[W,se]=Q0(D,Du(K0(p)));s=P0(V.body,W0,W,se)}}F.isString(S)||(S=S?"include":"omit");const w="credentials"in Request.prototype;E=new Request(a,{...x,signal:R,method:r.toUpperCase(),headers:g.normalize().toJSON(),body:s,duplex:"half",credentials:w?S:void 0});let T=await fetch(E);const M=Bf&&(m==="stream"||m==="response");if(Bf&&(h||M&&C)){const V={};["status","statusText","headers"].forEach(fe=>{V[fe]=T[fe]});const I=F.toFiniteNumber(T.headers.get("content-length")),[W,se]=h&&Q0(I,Du(K0(h),!0))||[];T=new Response(P0(T.body,W0,W,()=>{se&&se(),C&&C()}),V)}m=m||"text";let X=await Cu[F.findKey(Cu,m)||"text"](T,l);return!M&&C&&C(),await new Promise((V,I)=>{cg(V,I,{data:X,headers:tn.from(T.headers),status:T.status,statusText:T.statusText,config:l,request:E})})}catch(w){throw C&&C(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new Re("Network Error",Re.ERR_NETWORK,l,E),{cause:w.cause||w}):Re.from(w,w&&w.code,l,E)}}),kf={http:f2,xhr:V2,fetch:J2};F.forEach(kf,(l,a)=>{if(l){try{Object.defineProperty(l,"name",{value:a})}catch{}Object.defineProperty(l,"adapterName",{value:a})}});const I0=l=>`- ${l}`,P2=l=>F.isFunction(l)||l===null||l===!1,pg={getAdapter:l=>{l=F.isArray(l)?l:[l];const{length:a}=l;let r,s;const o={};for(let f=0;f<a;f++){r=l[f];let d;if(s=r,!P2(r)&&(s=kf[(d=String(r)).toLowerCase()],s===void 0))throw new Re(`Unknown adapter '${d}'`);if(s)break;o[d||"#"+f]=s}if(!s){const f=Object.entries(o).map(([h,p])=>`adapter ${h} `+(p===!1?"is not supported by the environment":"is not available in the build"));let d=a?f.length>1?`since :
`+f.map(I0).join(`
`):" "+I0(f[0]):"as no adapter specified";throw new Re("There is no suitable adapter to dispatch the request "+d,"ERR_NOT_SUPPORT")}return s},adapters:kf};function xf(l){if(l.cancelToken&&l.cancelToken.throwIfRequested(),l.signal&&l.signal.aborted)throw new Mi(null,l)}function ey(l){return xf(l),l.headers=tn.from(l.headers),l.data=Sf.call(l,l.transformRequest),["post","put","patch"].indexOf(l.method)!==-1&&l.headers.setContentType("application/x-www-form-urlencoded",!1),pg.getAdapter(l.adapter||Pr.adapter)(l).then(function(s){return xf(l),s.data=Sf.call(l,l.transformResponse,s),s.headers=tn.from(s.headers),s},function(s){return og(s)||(xf(l),s&&s.response&&(s.response.data=Sf.call(l,l.transformResponse,s.response),s.response.headers=tn.from(s.response.headers))),Promise.reject(s)})}const yg="1.9.0",$u={};["object","boolean","number","function","string","symbol"].forEach((l,a)=>{$u[l]=function(s){return typeof s===l||"a"+(a<1?"n ":" ")+l}});const ty={};$u.transitional=function(a,r,s){function o(f,d){return"[Axios v"+yg+"] Transitional option '"+f+"'"+d+(s?". "+s:"")}return(f,d,h)=>{if(a===!1)throw new Re(o(d," has been removed"+(r?" in "+r:"")),Re.ERR_DEPRECATED);return r&&!ty[d]&&(ty[d]=!0,console.warn(o(d," has been deprecated since v"+r+" and will be removed in the near future"))),a?a(f,d,h):!0}};$u.spelling=function(a){return(r,s)=>(console.warn(`${s} is likely a misspelling of ${a}`),!0)};function W2(l,a,r){if(typeof l!="object")throw new Re("options must be an object",Re.ERR_BAD_OPTION_VALUE);const s=Object.keys(l);let o=s.length;for(;o-- >0;){const f=s[o],d=a[f];if(d){const h=l[f],p=h===void 0||d(h,f,l);if(p!==!0)throw new Re("option "+f+" must be "+p,Re.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new Re("Unknown option "+f,Re.ERR_BAD_OPTION)}}const vu={assertOptions:W2,validators:$u},Wn=vu.validators;let Na=class{constructor(a){this.defaults=a||{},this.interceptors={request:new X0,response:new X0}}async request(a,r){try{return await this._request(a,r)}catch(s){if(s instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const f=o.stack?o.stack.replace(/^.+\n/,""):"";try{s.stack?f&&!String(s.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+f):s.stack=f}catch{}}throw s}}_request(a,r){typeof a=="string"?(r=r||{},r.url=a):r=a||{},r=za(this.defaults,r);const{transitional:s,paramsSerializer:o,headers:f}=r;s!==void 0&&vu.assertOptions(s,{silentJSONParsing:Wn.transitional(Wn.boolean),forcedJSONParsing:Wn.transitional(Wn.boolean),clarifyTimeoutError:Wn.transitional(Wn.boolean)},!1),o!=null&&(F.isFunction(o)?r.paramsSerializer={serialize:o}:vu.assertOptions(o,{encode:Wn.function,serialize:Wn.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),vu.assertOptions(r,{baseUrl:Wn.spelling("baseURL"),withXsrfToken:Wn.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let d=f&&F.merge(f.common,f[r.method]);f&&F.forEach(["delete","get","head","post","put","patch","common"],E=>{delete f[E]}),r.headers=tn.concat(d,f);const h=[];let p=!0;this.interceptors.request.forEach(function(C){typeof C.runWhen=="function"&&C.runWhen(r)===!1||(p=p&&C.synchronous,h.unshift(C.fulfilled,C.rejected))});const m=[];this.interceptors.response.forEach(function(C){m.push(C.fulfilled,C.rejected)});let g,S=0,x;if(!p){const E=[ey.bind(this),void 0];for(E.unshift.apply(E,h),E.push.apply(E,m),x=E.length,g=Promise.resolve(r);S<x;)g=g.then(E[S++],E[S++]);return g}x=h.length;let R=r;for(S=0;S<x;){const E=h[S++],C=h[S++];try{R=E(R)}catch(D){C.call(this,D);break}}try{g=ey.call(this,R)}catch(E){return Promise.reject(E)}for(S=0,x=m.length;S<x;)g=g.then(m[S++],m[S++]);return g}getUri(a){a=za(this.defaults,a);const r=fg(a.baseURL,a.url,a.allowAbsoluteUrls);return rg(r,a.params,a.paramsSerializer)}};F.forEach(["delete","get","head","options"],function(a){Na.prototype[a]=function(r,s){return this.request(za(s||{},{method:a,url:r,data:(s||{}).data}))}});F.forEach(["post","put","patch"],function(a){function r(s){return function(f,d,h){return this.request(za(h||{},{method:a,headers:s?{"Content-Type":"multipart/form-data"}:{},url:f,data:d}))}}Na.prototype[a]=r(),Na.prototype[a+"Form"]=r(!0)});let I2=class gg{constructor(a){if(typeof a!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(f){r=f});const s=this;this.promise.then(o=>{if(!s._listeners)return;let f=s._listeners.length;for(;f-- >0;)s._listeners[f](o);s._listeners=null}),this.promise.then=o=>{let f;const d=new Promise(h=>{s.subscribe(h),f=h}).then(o);return d.cancel=function(){s.unsubscribe(f)},d},a(function(f,d,h){s.reason||(s.reason=new Mi(f,d,h),r(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason){a(this.reason);return}this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;const r=this._listeners.indexOf(a);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const a=new AbortController,r=s=>{a.abort(s)};return this.subscribe(r),a.signal.unsubscribe=()=>this.unsubscribe(r),a.signal}static source(){let a;return{token:new gg(function(o){a=o}),cancel:a}}};function ew(l){return function(r){return l.apply(null,r)}}function tw(l){return F.isObject(l)&&l.isAxiosError===!0}const Hf={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Hf).forEach(([l,a])=>{Hf[a]=l});function vg(l){const a=new Na(l),r=Qy(Na.prototype.request,a);return F.extend(r,Na.prototype,a,{allOwnKeys:!0}),F.extend(r,a,null,{allOwnKeys:!0}),r.create=function(o){return vg(za(l,o))},r}const ht=vg(Pr);ht.Axios=Na;ht.CanceledError=Mi;ht.CancelToken=I2;ht.isCancel=og;ht.VERSION=yg;ht.toFormData=Yu;ht.AxiosError=Re;ht.Cancel=ht.CanceledError;ht.all=function(a){return Promise.all(a)};ht.spread=ew;ht.isAxiosError=tw;ht.mergeConfig=za;ht.AxiosHeaders=tn;ht.formToJSON=l=>ug(F.isHTMLForm(l)?new FormData(l):l);ht.getAdapter=pg.getAdapter;ht.HttpStatusCode=Hf;ht.default=ht;const{Axios:xw,AxiosError:Ew,CanceledError:ww,isCancel:Aw,CancelToken:_w,VERSION:Tw,all:Rw,Cancel:Ow,isAxiosError:jw,spread:Dw,toFormData:Cw,AxiosHeaders:Nw,HttpStatusCode:Mw,formToJSON:Uw,getAdapter:zw,mergeConfig:Lw}=ht,nw="http://127.0.0.1:8000/",bg=ht.create({baseURL:nw,timeout:5e3,headers:{"Content-Type":"application/json",accept:"application/json"}});bg.interceptors.request.use(l=>{const a=localStorage.getItem("access_token");return a&&(l.headers.Authorization=`JWT ${a}`),l},l=>Promise.reject(l));const lw=Je.createContext({}),Sg=!0;function aw({baseColor:l,highlightColor:a,width:r,height:s,borderRadius:o,circle:f,direction:d,duration:h,enableAnimation:p=Sg,customHighlightBackground:m}){const g={};return d==="rtl"&&(g["--animation-direction"]="reverse"),typeof h=="number"&&(g["--animation-duration"]=`${h}s`),p||(g["--pseudo-element-display"]="none"),(typeof r=="string"||typeof r=="number")&&(g.width=r),(typeof s=="string"||typeof s=="number")&&(g.height=s),(typeof o=="string"||typeof o=="number")&&(g.borderRadius=o),f&&(g.borderRadius="50%"),typeof l<"u"&&(g["--base-color"]=l),typeof a<"u"&&(g["--highlight-color"]=a),typeof m=="string"&&(g["--custom-highlight-background"]=m),g}function nl({count:l=1,wrapper:a,className:r,containerClassName:s,containerTestId:o,circle:f=!1,style:d,...h}){var p,m,g;const S=Je.useContext(lw),x={...h};for(const[M,X]of Object.entries(h))typeof X>"u"&&delete x[M];const R={...S,...x,circle:f},E={...d,...aw(R)};let C="react-loading-skeleton";r&&(C+=` ${r}`);const D=(p=R.inline)!==null&&p!==void 0?p:!1,w=[],T=Math.ceil(l);for(let M=0;M<T;M++){let X=E;if(T>l&&M===T-1){const I=(m=X.width)!==null&&m!==void 0?m:"100%",W=l%1,se=typeof I=="number"?I*W:`calc(${I} * ${W})`;X={...X,width:se}}const V=Je.createElement("span",{className:C,style:X,key:M},"‌");D?w.push(V):w.push(Je.createElement(Je.Fragment,{key:M},V,Je.createElement("br",null)))}return Je.createElement("span",{className:s,"data-testid":o,"aria-live":"polite","aria-busy":(g=R.enableAnimation)!==null&&g!==void 0?g:Sg},a?w.map((M,X)=>Je.createElement(a,{key:X},M)):w)}function Ef(){return v.jsxs(v.Fragment,{children:[v.jsx(nl,{height:349,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"}),v.jsx(nl,{height:443,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"}),v.jsx(nl,{height:500,width:350,borderRadius:40,baseColor:"#ffd700",highlightColor:"#909098"}),v.jsx(nl,{height:443,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"}),v.jsx(nl,{height:349,width:100,borderRadius:40,baseColor:"#ffe34c",highlightColor:"#909098"})]})}function iw(){const[l,a]=_.useState(!1),[r,s]=_.useState("Growing Industry Sector"),[o,f]=_.useState([]),[d,h]=_.useState([]),[p,m]=_.useState([]),[g,S]=_.useState([]),[x,R]=_.useState(null),[E,C]=_.useState(null),[D,w]=_.useState(!0);_.useEffect(()=>{(async()=>{const X=await Ml.getCurrentUser();C(X)})()},[]),_.useEffect(()=>{(async()=>{try{const X=await Zy.getLatestTrends();console.log("Latest trends:",X),f(X[0]),h(X[1]),m(X[2]),S(X[3]),w(!1)}catch(X){console.error("Failed to fetch latest trends:",X)}})()}),_.useEffect(()=>{r=="Growing Industry Sector"?R("Top 5 Growing Industry Sectors in"):r=="Industry Sector Revenue"?R("Top 5 Industry Sectors by Revenue in"):R("Top 5 Least Crowded Industry Sectors in")},[r]),l?document.body.style.overflow="hidden":document.body.style.overflow="auto";const T=(M,X,V)=>{if(V=="Growing Industry Sector")return o.find(I=>I.type===M&&I.rank===X);if(V=="Industry Sector Revenue")return d.find(I=>I.type===M&&I.rank===X);if(V=="Least Crowded")return p.find(I=>I.type===M&&I.rank===X)};return v.jsxs(v.Fragment,{children:[l&&v.jsx(_E,{showModal:()=>a(!1)}),v.jsx("nav",{children:v.jsx(ud,{showModal:()=>a(!0)})}),v.jsxs("main",{className:"home",children:[v.jsxs("section",{className:"short-term",id:"short-term",children:[v.jsxs("div",{className:"filter",children:[v.jsx("p",{children:"Filter Results"}),v.jsx("div",{className:"line",children:v.jsx("div",{className:`dot ${r=="Growing Industry Sector"?"active":""}`,onClick:()=>s("Growing Industry Sector"),children:v.jsx("span",{children:"Growing Industry Sector"})})}),v.jsx("div",{className:"line",children:v.jsx("div",{className:`dot ${r=="Industry Sector Revenue"?"active":""}`,onClick:()=>s("Industry Sector Revenue"),children:v.jsx("span",{children:"Industry Sector Revenue"})})}),v.jsx("div",{className:"line",children:v.jsx("div",{className:`dot ${r=="Least Crowded"?"active":""}`,onClick:()=>s("Least Crowded"),children:v.jsx("span",{children:"Least Crowded"})})})]}),v.jsxs("h1",{children:["Short-Term Outlook: ",x," ",g.length>0?g[0]:"🔮"]}),v.jsxs("section",{className:"short-term-contents",children:[D&&v.jsx(Ef,{}),!D&&v.jsxs(v.Fragment,{children:[v.jsx(Vt,{topNumber:4,type:"short-term",data:T("short-term",4,r),filterResult:r}),v.jsx(Vt,{topNumber:2,type:"short-term",data:T("short-term",2,r),filterResult:r}),v.jsx(Vt,{topNumber:1,type:"short-term",data:T("short-term",1,r),filterResult:r}),v.jsx(Vt,{topNumber:3,type:"short-term",data:T("short-term",3,r),filterResult:r}),v.jsx(Vt,{topNumber:5,type:"short-term",data:T("short-term",5,r),filterResult:r})]})]})]}),v.jsxs("section",{className:"mid-term",id:"mid-term",children:[v.jsxs("h1",{children:["Mid-Term Outlook: ",x," ",g.length>0?g[1]:"🔮"]}),v.jsxs("section",{className:"mid-term-contents",children:[D&&v.jsx(Ef,{}),!D&&v.jsxs(v.Fragment,{children:[v.jsx(Vt,{topNumber:4,type:"mid-term",data:T("mid-term",4,r),filterResult:r,color:"dark"}),v.jsx(Vt,{topNumber:2,type:"mid-term",data:T("mid-term",2,r),filterResult:r,color:"dark"}),v.jsx(Vt,{topNumber:1,type:"mid-term",data:T("mid-term",1,r),filterResult:r,color:"dark"}),v.jsx(Vt,{topNumber:3,type:"mid-term",data:T("mid-term",3,r),filterResult:r,color:"dark"}),v.jsx(Vt,{topNumber:5,type:"mid-term",data:T("mid-term",5,r),filterResult:r,color:"dark"})]})]})]}),v.jsxs("section",{className:"long-term",id:"long-term",children:[v.jsxs("h1",{children:["Long-Term Outlook: ",x," ",g.length>0?g[2]:"🔮"," ","(Based on Business Count)"]}),v.jsxs("section",{className:"long-term-contents",children:[D&&v.jsx(Ef,{}),!D&&v.jsxs(v.Fragment,{children:[v.jsx(Vt,{topNumber:4,type:"long-term",data:T("long-term",4,r),filterResult:r}),v.jsx(Vt,{topNumber:2,type:"long-term",data:T("long-term",2,r),filterResult:r}),v.jsx(Vt,{topNumber:1,type:"long-term",data:T("long-term",1,r),filterResult:r}),v.jsx(Vt,{topNumber:3,type:"long-term",data:T("long-term",3,r),filterResult:r}),v.jsx(Vt,{topNumber:5,type:"long-term",data:T("long-term",5,r),filterResult:r})]})]})]})]})]})}function rw(){const l=Vn(),[a,r]=_.useState([]),s=ji().shape({email:It().required("Email is required.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format."),password:It().required("Password is required.").min(12).max(16).matches(/^(?=.*\d{1})(?=.*[a-z]{1})(?=.*[A-Z]{1})(?=.*[!@#$%^&*{|}?~_=+.-]{1})(?=.*[^a-zA-Z0-9])(?!.*\s).{12,16}$/),confirmPassword:It().oneOf([ed("password"),null],"Password don't match.").required("Confirm Password is required.")}),{register:o,handleSubmit:f,formState:{errors:d},watch:h}=$r({resolver:Xr(s),mode:"all"}),p=g=>{console.log(g),bg.post("register/",{email:g.email,password:g.confirmPassword}).then(()=>{l("/login")})},m=h("password","");return _.useEffect(()=>{let g=[];m.length<12&&g.push("- At least 12 characters."),m.length>16&&g.push("- Not exceed 16 characters."),/[0-9]/.test(m)||g.push("- At least one number."),/[a-z]/.test(m)||g.push("- At least one lowercase letter."),/[A-Z]/.test(m)||g.push("- At least one uppercase letter."),/[!@#$%^&*{|}?~_=+.-]/.test(m)||g.push("- At least one special character (!@#$%^&*{|}?~_=+.-)"),/\s/.test(m)&&g.push("Must not contain spaces"),r(g)},[m]),v.jsx("main",{className:"register-page",children:v.jsxs("form",{onSubmit:f(p),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),d.email&&v.jsx("span",{children:d.email.message}),v.jsx("input",{type:"email",name:"email",id:"email",placeholder:"Enter email...",...o("email")})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"Password:"}),a.length!==0&&v.jsxs("div",{className:"password-errors-container",children:[v.jsx("span",{children:"Password must contain the following:"}),a.map((g,S)=>v.jsx("span",{children:g},S))]}),v.jsx("input",{type:"password",name:"password",id:"password",placeholder:"Enter password...",...o("password")})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"confirm-password",children:"Confirm Password:"}),d.confirmPassword&&v.jsx("span",{children:d.confirmPassword.message}),v.jsx("input",{type:"password",name:"confirm-password",id:"confirm-password",placeholder:"Enter confirm password...",...o("confirmPassword")})]}),v.jsx("button",{type:"submit",children:"Create Account"})]})})}function sw(){const l=localStorage.getItem("access_token");return l?sessionStorage.getItem("current_user")&&!JSON.parse(sessionStorage.getItem("current_user")).is_superuser?v.jsx(uf,{to:"/"}):l?v.jsx(O1,{}):v.jsx(uf,{to:"/"}):v.jsx(uf,{to:"/"})}const wf="http://127.0.0.1:8000/",uw="http://127.0.0.1:8000/users/list_by_status/?is_active=";class ow{async register(a,r,s,o,f){try{const d=await fetch(wf+"register/",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:a,password:r,first_name:s,last_name:o,is_superuser:f})});return d.ok,d}catch(d){console.log("Failed to register!",d)}}async getAllUsers(){try{const a=await fetch(wf+"users/",{method:"GET",headers:Ml.getAuthHeader()});if(!a.ok){const s=await a.json();return console.log("failed to fetch all users"),s}const r=await a.json();return Array.from(r)}catch(a){console.log("Failed to get all users!",a)}}async getUsersByisActive(a){try{const r=await fetch(uw+a,{method:"GET",headers:Ml.getAuthHeader()});if(!r.ok){const o=await r.json();return console.log("failed to fetch is_active users"),o}const s=await r.json();return Array.from(s)}catch(r){console.log("Failed to get users by status!",r)}}async changeUserStatus(a){try{const r=await fetch(wf+"users/change_status/",{method:"PATCH",headers:Ml.getAuthHeader(),body:JSON.stringify({user_id:a})});if(!r.ok){const s=await r.json();return console.log("failed to change user status",s),!1}return!0}catch(r){console.log("Failed to change user status!",r)}}}const Br=new ow;function cw({showRegisterUserModal:l}){const a=Vn(),[r,s]=_.useState(!1),[o,f]=_.useState([]),[d,h]=_.useState(!1),[p,m]=_.useState(!1),g=ji().shape({firstName:It().required("First name is required."),lastName:It().required("Last name is required."),email:It().required("Email is required.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format."),password:It().required("Password is required.").matches(/^(?=.*\d{1})(?=.*[a-z]{1})(?=.*[A-Z]{1})(?=.*[!@#$%^&*{|}?~_=+.-]{1})(?=.*[^a-zA-Z0-9])(?!.*\s).{12,16}$/),confirmPassword:It().required("Confirm password is required.").oneOf([ed("password"),null],"Password don't match.")}),{register:S,handleSubmit:x,formState:{errors:R,isValid:E},watch:C}=$r({resolver:Xr(g),mode:"all"}),D=async T=>{h(!0);try{(await Br.register(T.email,T.password,T.firstName,T.lastName,T.isAdmin)).ok?(a("/user-management",{state:{registrationSuccess:!0}}),l()):m(!0)}catch(M){console.error("Error during registration:",M)}finally{h(!1)}},w=C("password","");return _.useEffect(()=>{let T=[];w.length<12&&T.push("- At least 12 characters."),w.length>16&&T.push("- Not exceed 16 characters."),/[0-9]/.test(w)||T.push("- At least one number."),/[a-z]/.test(w)||T.push("- At least one lowercase letter."),/[A-Z]/.test(w)||T.push("- At least one uppercase letter."),/[!@#$%^&*{|}?~_=+.-]/.test(w)||T.push("- At least one special character (!@#$%^&*{|}?~_=+.-)"),/\s/.test(w)&&T.push("Must not contain spaces"),f(T)},[w]),_.useEffect(()=>{p&&setTimeout(()=>{m(!1)},5e3)},[p]),v.jsxs(v.Fragment,{children:[p&&v.jsx(Hr,{message:"Registration failed. Please try again.",type:"danger"}),v.jsxs("div",{className:"register-user-modal",children:[v.jsx("section",{className:"overlay",onClick:l}),v.jsxs("section",{className:"content",children:[v.jsxs("header",{children:[v.jsx("button",{onMouseEnter:()=>s(!0),onMouseLeave:()=>s(!1),onClick:l,children:v.jsx("img",{src:r?zu:ju,alt:"close-icon"})}),v.jsx("h2",{children:"Register New User"})]}),v.jsxs("form",{onSubmit:x(D),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"first-name",children:"First Name *"}),v.jsx("input",{type:"text",name:"first-name",id:"first-name",required:!0,placeholder:"Enter first name...",...S("firstName")}),R.firstName&&v.jsx("span",{children:R.firstName.message})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"last-name",children:"Last Name *"}),v.jsx("input",{type:"text",name:"last-name",id:"last-name",required:!0,placeholder:"Enter last name...",...S("lastName")}),R.lastName&&v.jsx("span",{children:R.lastName.message})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email *"}),v.jsx("input",{type:"email",name:"email",id:"email",required:!0,placeholder:"Enter email...",...S("email")}),R.email&&v.jsx("span",{children:R.email.message})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"Password *"}),v.jsx("input",{type:"password",name:"password",id:"password",required:!0,placeholder:"Enter password...",...S("password")}),o.length!==0&&v.jsxs("div",{className:"password-errors-container",children:[v.jsx("span",{children:"Password must contain the following:"}),o.map((T,M)=>v.jsx("span",{children:T},M))]})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"confirm-password",children:"Confirm Password *"}),v.jsx("input",{type:"password",name:"confirm-password",id:"confirm-password",required:!0,placeholder:"Enter confirm password...",...S("confirmPassword")}),R.confirmPassword&&v.jsx("span",{children:R.confirmPassword.message})]}),v.jsxs("fieldset",{children:[v.jsx("input",{type:"checkbox",name:"is-admin",id:"is-admin",...S("isAdmin")}),v.jsx("label",{htmlFor:"is-admin",children:"Click the checkbox if the new user is an admin."})]}),v.jsxs("button",{className:"submit-button",disabled:!E||d,children:[d&&v.jsx(Di,{}),d?"Registering...":"Register"]})]})]})]})]})}const fw="/assets/active-user-Bd-Ue2Qb.svg",dw="/assets/delete-account-BwubX78G.svg";function hw({isShow:l,action:a,userId:r}){let s;const o=Vn(),[f,d]=_.useState(!1),[h,p]=_.useState(!1);a==="Activate"?s=fw:s=dw;const m=async()=>{p(!0);try{if(await Br.changeUserStatus(r))o("/user-management",{state:{changeStatusSuccess:!0}}),l();else return}catch(g){console.log("Failed to change user status!",g)}finally{p(!1)}};return v.jsxs("section",{className:"confirm-status-change-modal",children:[v.jsx("section",{className:"overlay",onClick:l}),v.jsxs("section",{className:"content",children:[v.jsx("button",{onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1),onClick:l,className:"close-btn",children:v.jsx("img",{src:f?zu:ju,alt:"close-icon"})}),v.jsx("img",{src:s,alt:"icon","data-action":a}),v.jsxs("h2",{children:[a," Account"]}),v.jsxs("p",{children:["Are you sure you want to ",a.toLowerCase()," the account of this user?"]}),v.jsxs("div",{className:"group-buttons",children:[v.jsxs("button",{className:"submit-button",onClick:l,children:["No, keep ",a.toLowerCase()]}),v.jsxs("button",{className:"submit-button",onClick:m,disabled:h,children:[h&&v.jsx(Di,{}),h?"Processing...":`Yes, ${a.toLowerCase()}`]})]})]})]})}function mw(){var V,I;const a=sl(),r=Vn(),[s,o]=_.useState("All"),[f,d]=_.useState(!1),[h,p]=_.useState(!1),[m,g]=_.useState([]),[S,x]=_.useState(!0),[R,E]=_.useState(!1),[C,D]=_.useState({userId:null,action:null}),[w,T]=_.useState(!1),M=(V=a.state)==null?void 0:V.registrationSuccess,X=(I=a.state)==null?void 0:I.changeStatusSuccess;return _.useEffect(()=>{M&&(p(!0),setTimeout(()=>{p(!1),r(a.pathname,{state:{registrationSuccess:!1}})},5e3)),X&&(T(!0),setTimeout(()=>{T(!1),r(a.pathname,{state:{changeStatusSuccess:!1}})},5e3))},[M,X]),_.useEffect(()=>{g([]),x(!0),(async()=>{if(s==="All"){const se=await Br.getAllUsers();g(se),x(!1),console.log("Users All:",se)}else if(s==="Active"){const se=await Br.getUsersByisActive(1);g(se),x(!1),console.log("Users Active:",se)}else if(s==="Inactive"){const se=await Br.getUsersByisActive(0);g(se),x(!1),console.log("Users Inactive:",se)}})()},[s]),v.jsxs(v.Fragment,{children:[f&&v.jsx(cw,{showRegisterUserModal:()=>d(!1)}),h&&v.jsx(Hr,{message:"Registration successful!",type:"success"}),R&&v.jsx(hw,{isShow:()=>E(!1),action:C.action,userId:C.userId}),w&&v.jsx(Hr,{message:"User status changed successfully!",type:"success"}),v.jsx("nav",{children:v.jsx(ud,{})}),v.jsxs("main",{className:"user-management",children:[v.jsx("section",{children:v.jsxs("div",{className:"title-page",children:[v.jsx("h1",{children:"User Management"}),v.jsx("button",{onClick:()=>d(!0),children:"Add User"})]})}),v.jsx("section",{children:v.jsxs("div",{className:"table-header",children:[v.jsxs("h2",{children:[s," Users ",!S&&v.jsxs("span",{children:["(",m.length,")"]})]}),v.jsxs("ul",{children:[v.jsx("li",{className:s==="All"?"active":"",onClick:()=>o("All"),children:"All"}),v.jsx("li",{className:s==="Active"?"active":"",onClick:()=>o("Active"),children:"Active"}),v.jsx("li",{className:s==="Inactive"?"active":"",onClick:()=>o("Inactive"),children:"Inactive"})]})]})}),S&&v.jsx("section",{className:"grid-container",children:Array.from({length:8}).map((W,se)=>v.jsxs("article",{className:"user-container",children:[v.jsx(nl,{height:40,width:40,circle:!0}),v.jsx(nl,{height:20,width:100}),v.jsxs("section",{className:"status-and-role",children:[v.jsx(nl,{height:20,width:60}),v.jsx(nl,{height:20,width:60})]}),v.jsx(nl,{height:40,width:200,borderRadius:40})]},se))}),!S&&m.length==0&&v.jsx("p",{children:"No users found."}),m.length>0&&v.jsx("section",{className:"grid-container",children:m.map((W,se)=>v.jsxs("article",{className:"user-container",children:[v.jsx("img",{src:_u,alt:"Profile"}),v.jsxs("h2",{children:[W.first_name," ",W.last_name]}),v.jsxs("section",{className:"status-and-role",children:[v.jsxs("span",{className:"status","data-status":W.is_active===!0?"active":"inactive",children:[v.jsx("div",{className:"dot"}),v.jsx("p",{children:W.is_active===!0?"Active":"Inactive"})]}),W.is_superuser===!0&&v.jsxs("span",{className:"role","data-role":"admin",children:[v.jsx("div",{className:"dot"}),v.jsx("p",{children:"Admin"})]})]}),v.jsx("button",{onClick:()=>{E(!0),D({userId:W.id,action:W.is_active===!0?"Deactivate":"Activate"})},children:W.is_active===!0?"Deactivate Account":"Activate Account"})]},se))})]})]})}function pw(){return v.jsxs(v.Fragment,{children:[v.jsx("nav",{children:v.jsx(ud,{})}),v.jsxs("main",{className:"account-details",children:[v.jsx("section",{children:v.jsxs("div",{className:"title-page",children:[v.jsx("h1",{children:"Account Profile"}),v.jsxs("div",{className:"profile-container",children:[v.jsx("h4",{children:"Mary Grace Piattos"}),v.jsx("img",{src:_u,alt:""})]})]})}),v.jsxs("section",{children:[v.jsxs("section",{className:"left-panel",children:[v.jsx("img",{src:_u,alt:""}),v.jsx("button",{children:"Change Profile Picture"}),v.jsxs("form",{action:"",children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"first-name",children:"First name"}),v.jsx("input",{type:"text",name:"first-name",id:"first-name"})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"last-name",children:"Last name"}),v.jsx("input",{type:"text",name:"last-name",id:"last-name"})]}),v.jsx("button",{type:"submit",children:"Save Changes"})]})]}),v.jsxs("section",{children:[v.jsx("h2",{children:"Account Overview"}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),v.jsx("input",{type:"text",name:"email",id:"email",placeholder:"m*****@gmail.com",disabled:!0})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"role",children:"Role:"}),v.jsx("input",{type:"text",name:"role",id:"role",placeholder:"User",disabled:!0})]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"date-created",children:"Date Created:"}),v.jsx("input",{type:"text",name:"date-created",id:"date-created",placeholder:"2024-01-01",disabled:!0})]})]})]})]})]})}const xg="/assets/forgot_password-jtzYLH6S.png";function yw(){const l=Vn(),[a,r]=_.useState(!1),[s,o]=_.useState(null),f=ji().shape({email:It().required("Email is required.").email("Invalid email format.").matches(/^\S+@\S+\.\S+$/,"Invalid email format.")}),{register:d,handleSubmit:h,formState:{errors:p,isValid:m}}=$r({resolver:Xr(f),mode:"all"}),g=async S=>{r(!0);const x=await Ml.resetPassword(S.email);o(x),r(!1)};return _.useEffect(()=>{s&&setTimeout(()=>{o(!1)},5e3)},[s]),v.jsxs(v.Fragment,{children:[s&&v.jsx(Hr,{message:`If that email address is in our database, \r
          we will send you an email to reset your password.`,type:"success"}),v.jsxs("main",{className:"reset-password",children:[v.jsx("section",{className:"left-panel",children:v.jsx("img",{src:xg,alt:"log-in"})}),v.jsxs("section",{className:"right-panel",children:[v.jsx("h1",{children:"Password Reset"}),v.jsx("p",{children:"Enter your email address and we will send you a link to reset your password."}),v.jsxs("form",{onSubmit:h(g),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"email",children:"Email:"}),v.jsx("input",{type:"email",name:"email",id:"email",placeholder:"Enter email...",...d("email")}),p.email&&v.jsx("span",{children:p.email.message})]}),v.jsxs("button",{type:"submit",disabled:!m||a,className:"submit-button",children:[a&&v.jsx(Di,{}),a?"Submitting...":"Sent Reset Link"]}),v.jsx("a",{onClick:()=>l("/login"),children:"Back to Log In"})]})]})]})]})}function gw({match:l}){const a=Vn(),[r,s]=_.useState(!1),[o,f]=_.useState([]),[d,h]=_.useState(null),p=ji().shape({password:It().required("Password is required.").min(12).max(16).matches(/^(?=.*\d{1})(?=.*[a-z]{1})(?=.*[A-Z]{1})(?=.*[!@#$%^&*{|}?~_=+.-]{1})(?=.*[^a-zA-Z0-9])(?!.*\s).{12,16}$/),confirmPassword:It().oneOf([ed("password"),null],"Password don't match.").required("Confirm Password is required.")}),{register:m,handleSubmit:g,formState:{errors:S,isValid:x},watch:R}=$r({resolver:Xr(p),mode:"all"}),E=async D=>{s(!0);const w=l.params.uid,T=l.params.token;await Ml.resetPasswordConfirm(w,T,D.password)&&(s(!1),h(!0)),console.log(D)};_.useEffect(()=>{d&&setTimeout(()=>{h(!1)},5e3)},[d]);const C=R("password","");return _.useEffect(()=>{let D=[];C.length<12&&D.push("- At least 12 characters."),C.length>16&&D.push("- Not exceed 16 characters."),/[0-9]/.test(C)||D.push("- At least one number."),/[a-z]/.test(C)||D.push("- At least one lowercase letter."),/[A-Z]/.test(C)||D.push("- At least one uppercase letter."),/[!@#$%^&*{|}?~_=+.-]/.test(C)||D.push("- At least one special character (!@#$%^&*{|}?~_=+.-)"),/\s/.test(C)&&D.push("Must not contain spaces"),f(D)},[C]),v.jsx(v.Fragment,{children:v.jsxs("main",{className:"reset-password",children:[v.jsx("section",{className:"left-panel",children:v.jsx("img",{src:xg,alt:"log-in"})}),v.jsxs("section",{className:"right-panel",children:[v.jsx("h1",{children:"Set-Up New Password"}),v.jsx("p",{children:"Set a new password for your account."}),v.jsxs("form",{onSubmit:g(E),children:[v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"password",children:"New Password:"}),v.jsx("input",{type:"password",name:"password",id:"password",placeholder:"Enter new password...",...m("password")})]}),o.length!==0&&v.jsxs("div",{className:"password-errors-container",children:[v.jsx("span",{children:"Password must contain the following:"}),o.map((D,w)=>v.jsx("span",{children:D},w))]}),v.jsxs("fieldset",{children:[v.jsx("label",{htmlFor:"confirm-password",children:"Confirm Password:"}),v.jsx("input",{type:"password",name:"confirm-password",id:"confirm-password",placeholder:"Enter confirm password...",...m("confirmPassword")}),S.confirmPassword&&v.jsx("span",{children:S.confirmPassword.message})]}),v.jsxs("button",{type:"submit",disabled:!x||r,className:"submit-button",children:[r&&v.jsx(Di,{}),r?"Submitting...":"Submit"]}),v.jsx("a",{onClick:()=>a("/login"),children:"Back to Log In"})]})]})]})})}function vw(){return v.jsx(eS,{children:v.jsxs(D1,{children:[v.jsx(In,{path:"/",element:v.jsx(mS,{})}),v.jsx(In,{path:"/login",element:v.jsx(Ex,{})}),v.jsx(In,{path:"/register",element:v.jsx(rw,{})}),v.jsx(In,{path:"/home",element:v.jsx(iw,{})}),v.jsx(In,{path:"/account",element:v.jsx(pw,{})}),v.jsx(In,{path:"/reset-password",element:v.jsx(yw,{})}),v.jsx(In,{path:"/password/reset/confirm/:uid/:token",element:v.jsx(gw,{})}),v.jsx(In,{element:v.jsx(sw,{}),children:v.jsx(In,{path:"/user-management",element:v.jsx(mw,{})})})]})})}Bb.createRoot(document.getElementById("root")).render(v.jsx(_.StrictMode,{children:v.jsx(vw,{})}));

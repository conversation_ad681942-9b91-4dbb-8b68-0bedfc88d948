.reset-password {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background-color: var(--primary-color);
}

.reset-password section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 50vw;
}

.reset-password .left-panel {
  background-color: var(--primary-color);
}

.reset-password .left-panel img {
  height: 50%;
  width: 50%;
  object-fit: contain;

  mix-blend-mode: multiply;
}

.reset-password .right-panel {
  padding: 70px;
  gap: 10px;

  border: 1px solid #d3d3d3;
}

.reset-password .right-panel form {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  width: 100%;
  margin-top: 10px;

  gap: 10px;
}

.reset-password .right-panel form fieldset {
  display: flex;
  flex-direction: column;

  gap: 5px;
  width: 100%;
}

.reset-password .right-panel form input {
  display: flex;
  height: 44px;
  padding: 20px;
  background-color: var(--secondary-color);
  border-radius: 40px;
  border: 1px solid #d3d3d3;
}

.reset-password a {
  align-self: center;
  color: var(--btn-color);
  cursor: pointer;
}

.reset-password a:hover {
  color: var(--btn-hover-color);
}

.reset-password span {
  font-size: 0.875rem;
  color: red;
}

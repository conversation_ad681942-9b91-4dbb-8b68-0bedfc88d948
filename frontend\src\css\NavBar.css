nav {
  display: flex;
  justify-content: center;
  height: 60px;
  width: 100%;

  background-color: var(--primary-dark-color);
  /* border-bottom: 1px solid rgba(43, 43, 63, 1); */
  /* box-shadow: 0px 0px 80px 30px rgba(43, 43, 63, 0.3); */
  box-shadow: 0px 0px 10px 5px rgba(43, 43, 63, 0.3);

  position: fixed;
  top: 0;
  z-index: 1;
}

nav ul {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 50px;
  list-style: none;
  background-color: transparent;
}

nav li {
  color: rgba(248, 250, 252, 0.8);
  background-color: transparent;
  padding: 8px 12px;
  border-radius: 40px;
  cursor: pointer;
}

nav ul .active {
  background-color: var(--btn-color);
  box-shadow: 0px 0px 0px 4px rgba(79, 70, 229, 0.5),
    0px 0px 50px 20px rgba(79, 70, 229, 0.2);
  color: var(--primary-color);
  transition: all 0.5s ease;
}

nav a {
  text-decoration: none;
  color: inherit;
}

nav ul .system-icon {
  position: absolute;
  left: 35px;

  cursor: default;
}

nav li:last-child img {
  display: flex;
  height: 35px;
  width: 35px;

  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 0 0 2px var(--primary-dark-color),
    0 0 0 3px var(--primary-color);

  position: absolute;
  top: 13px;
  right: 38px;

  transition: 0.5s ease;
}

nav li:last-child img:hover {
  box-shadow: 0 0 0 2px var(--btn-color), 0 0 0 4px rgba(79, 70, 229, 0.5),
    0px 0px 50px 20px rgba(79, 70, 229, 0.2);
}

nav .tooltip {
  color: var(--primary-text-color);
  background-color: var(--primary-color);
  border-radius: 10px;
}

nav .profile-menu-container {
  display: flex;
  width: fit-content;
  height: fit-content;
  padding-top: 30px;

  position: absolute;
  right: 48px;
  bottom: -145px;

  cursor: default;
}

nav .profile-menu-container.User {
  display: flex;
  width: fit-content;
  height: fit-content;
  padding-top: 30px;

  position: absolute;
  right: 48px;
  top: 39px;

  cursor: default;
}

nav .view-as-guest {
  padding: 10px 16px;
  border-radius: 40px;
  background-color: var(--forecast-active-color);
  font-weight: 600;

  position: absolute;
  right: 48px;
}

nav .profile-menu {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  background-color: var(--primary-color);
  border-radius: 20px 0px 20px 20px;
  box-shadow: 0 0 50px 0 rgba(35, 35, 51, 0.2);
  padding: 12px;

  animation: fadeIn 0.5s ease-in-out;
}

nav .profile-menu li {
  color: var(--secondary-text-color);

  border-radius: 0px 10px 10px 0px;

  transition: 0.5s ease;
}

nav .profile-menu li:last-child {
  color: rgba(255, 0, 0, 0.8);

  transition: 0.5s ease;
}

nav .profile-menu li:hover {
  background-color: rgba(79, 70, 229, 0.05);
  box-shadow: -4px 0 0 var(--btn-color);

  color: var(--btn-color);
}

nav .profile-menu li:last-child:hover {
  background-color: rgba(255, 0, 0, 0.05);
  box-shadow: -4px 0 0 red;

  color: red;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
